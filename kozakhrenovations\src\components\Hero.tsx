import Link from 'next/link'

export default function Hero() {
  return (
    <section className="relative bg-gradient-to-r from-kozak-charcoal to-gray-800 text-white">
      <div className="absolute inset-0 bg-black opacity-50"></div>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-heading font-bold mb-6">
            Master Craftsmanship in Every Detail
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-gray-200">
            Your Vision, Built to Last
          </p>
          <p className="text-lg mb-10 max-w-3xl mx-auto text-gray-300">
            Professional home renovations across Nottinghamshire, Derbyshire, and Leicestershire. 
            From kitchen fitting to loft conversions, we deliver uncompromising quality with traditional craftsmanship values.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn-primary text-lg px-8 py-4">
              Get Your Free Quote
            </Link>
            <Link href="/our-work" className="btn-secondary text-lg px-8 py-4">
              View Our Work
            </Link>
          </div>
        </div>
      </div>
      
      {/* Background placeholder - replace with actual hero image */}
      <div className="absolute inset-0 -z-10">
        <div className="w-full h-full bg-gradient-to-br from-kozak-charcoal via-gray-700 to-kozak-orange opacity-20"></div>
      </div>
    </section>
  )
}
