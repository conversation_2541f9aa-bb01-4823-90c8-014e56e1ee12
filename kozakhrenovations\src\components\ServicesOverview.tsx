import Link from 'next/link'

const services = [
  {
    title: 'Kitchen Fitting',
    description: 'Complete kitchen transformations from design to installation',
    href: '/services/kitchen-fitting',
    icon: '🏠'
  },
  {
    title: 'Bathroom Renovations',
    description: 'Full bathroom renovations from brickwork up',
    href: '/services/bathroom-renovations',
    icon: '🚿'
  },
  {
    title: 'Loft Conversions',
    description: 'Transform your loft into valuable living space',
    href: '/services/loft-conversions',
    icon: '🏗️'
  },
  {
    title: 'External Wall Insulation',
    description: 'Improve energy efficiency and exterior finish',
    href: '/services/external-wall-insulation',
    icon: '🏡'
  },
  {
    title: 'General Renovations',
    description: 'Windows, doors, floors, stairs, plastering, tiling, painting',
    href: '/services/general-renovations',
    icon: '🔨'
  }
]

export default function ServicesOverview() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4">
            Our Services
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            From complete renovations to specialized installations, we deliver exceptional craftsmanship 
            across all aspects of home improvement.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Link
              key={index}
              href={service.href}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 group"
            >
              <div className="text-4xl mb-4">{service.icon}</div>
              <h3 className="text-xl font-heading font-semibold text-kozak-charcoal mb-3 group-hover:text-kozak-orange transition-colors">
                {service.title}
              </h3>
              <p className="text-gray-600 mb-4">
                {service.description}
              </p>
              <div className="text-kozak-orange font-semibold group-hover:underline">
                Learn More →
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}
