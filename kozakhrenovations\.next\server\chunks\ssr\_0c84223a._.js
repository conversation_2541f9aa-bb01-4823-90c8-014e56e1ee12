module.exports=[790,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(11857);a.n(d("[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>"))},84707,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(11857);a.n(d("[project]/node_modules/next/dist/client/app-dir/link.js"))},97647,a=>{"use strict";a.i(790);var b=a.i(84707);a.n(b)},15736,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/Header.tsx <module evaluation>","default")},58097,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/Header.tsx","default")},22777,a=>{"use strict";a.i(15736);var b=a.i(58097);a.n(b)},27572,a=>{"use strict";a.s(["default",()=>g,"metadata",()=>f],27572);var b=a.i(7997),c=a.i(22777),d=a.i(97647);function e(){return(0,b.jsx)("footer",{className:"bg-kozak-charcoal text-white",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,b.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,b.jsxs)("div",{className:"text-2xl font-heading font-bold mb-4",children:["Kozak ",(0,b.jsx)("span",{className:"text-kozak-orange",children:"Home Renovations"})]}),(0,b.jsx)("p",{className:"text-gray-300 mb-4",children:"Master craftsmanship in every detail. Professional home renovations across Nottinghamshire, Derbyshire, and Leicestershire."}),(0,b.jsxs)("div",{className:"space-y-2 text-gray-300",children:[(0,b.jsx)("p",{children:"📞 07849768183"}),(0,b.jsx)("p",{children:"✉️ <EMAIL>"}),(0,b.jsx)("p",{children:"🕒 Monday - Friday: 8:00 AM - 8:00 PM"}),(0,b.jsx)("p",{children:"📍 Serving Nottinghamshire, Derbyshire & Leicestershire"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-heading font-semibold mb-4",children:"Services"}),(0,b.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/services/kitchen-fitting",className:"hover:text-kozak-orange transition-colors",children:"Kitchen Fitting"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/services/bathroom-renovations",className:"hover:text-kozak-orange transition-colors",children:"Bathroom Renovations"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/services/loft-conversions",className:"hover:text-kozak-orange transition-colors",children:"Loft Conversions"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/services/external-wall-insulation",className:"hover:text-kozak-orange transition-colors",children:"External Wall Insulation"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/services/general-renovations",className:"hover:text-kozak-orange transition-colors",children:"General Renovations"})})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-heading font-semibold mb-4",children:"Quick Links"}),(0,b.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/our-work",className:"hover:text-kozak-orange transition-colors",children:"Our Work"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/about",className:"hover:text-kozak-orange transition-colors",children:"About Us"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/testimonials",className:"hover:text-kozak-orange transition-colors",children:"Testimonials"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/contact",className:"hover:text-kozak-orange transition-colors",children:"Contact Us"})}),(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:"/blog",className:"hover:text-kozak-orange transition-colors",children:"Blog"})})]})]})]}),(0,b.jsxs)("div",{className:"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400",children:[(0,b.jsx)("p",{children:"© 2024 Kozak Home Renovations. All rights reserved."}),(0,b.jsx)("p",{className:"mt-2",children:"Professional home improvements with traditional craftsmanship values."})]})]})})}let f={title:"Kozak Home Renovations | Expert Home Improvements in Nottinghamshire, Derbyshire & Leicestershire",description:"Professional home renovations by Marcin Kozak. Specializing in kitchen fitting, bathroom renovations, loft conversions, and external wall insulation across Nottinghamshire, Derbyshire & Leicestershire.",keywords:"home renovations, kitchen fitting, bathroom renovation, loft conversion, external wall insulation, Nottinghamshire, Derbyshire, Leicestershire"};function g({children:a}){return(0,b.jsx)("html",{lang:"en",children:(0,b.jsxs)("body",{className:"min-h-screen flex flex-col antialiased",children:[(0,b.jsx)(c.default,{}),(0,b.jsx)("main",{className:"flex-grow",children:a}),(0,b.jsx)(e,{})]})})}}];

//# sourceMappingURL=_0c84223a._.js.map