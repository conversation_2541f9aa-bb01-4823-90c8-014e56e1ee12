import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-kozak-charcoal text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="text-2xl font-heading font-bold mb-4">
              Kozak <span className="text-kozak-orange">Home Renovations</span>
            </div>
            <p className="text-gray-300 mb-4">
              Master craftsmanship in every detail. Professional home renovations across 
              Nottinghamshire, Derbyshire, and Leicestershire.
            </p>
            <div className="space-y-2 text-gray-300">
              <p>📞 07849768183</p>
              <p>✉️ <EMAIL></p>
              <p>🕒 Monday - Friday: 8:00 AM - 8:00 PM</p>
              <p>📍 Serving Nottinghamshire, Derbyshire & Leicestershire</p>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-heading font-semibold mb-4">Services</h3>
            <ul className="space-y-2 text-gray-300">
              <li>
                <Link href="/services/kitchen-fitting" className="hover:text-kozak-orange transition-colors">
                  Kitchen Fitting
                </Link>
              </li>
              <li>
                <Link href="/services/bathroom-renovations" className="hover:text-kozak-orange transition-colors">
                  Bathroom Renovations
                </Link>
              </li>
              <li>
                <Link href="/services/loft-conversions" className="hover:text-kozak-orange transition-colors">
                  Loft Conversions
                </Link>
              </li>
              <li>
                <Link href="/services/external-wall-insulation" className="hover:text-kozak-orange transition-colors">
                  External Wall Insulation
                </Link>
              </li>
              <li>
                <Link href="/services/general-renovations" className="hover:text-kozak-orange transition-colors">
                  General Renovations
                </Link>
              </li>
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-heading font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2 text-gray-300">
              <li>
                <Link href="/our-work" className="hover:text-kozak-orange transition-colors">
                  Our Work
                </Link>
              </li>
              <li>
                <Link href="/about" className="hover:text-kozak-orange transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/testimonials" className="hover:text-kozak-orange transition-colors">
                  Testimonials
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-kozak-orange transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/blog" className="hover:text-kozak-orange transition-colors">
                  Blog
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 Kozak Home Renovations. All rights reserved.</p>
          <p className="mt-2">Professional home improvements with traditional craftsmanship values.</p>
        </div>
      </div>
    </footer>
  )
}
