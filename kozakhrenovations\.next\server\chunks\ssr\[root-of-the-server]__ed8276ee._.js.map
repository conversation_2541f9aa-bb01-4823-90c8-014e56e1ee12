{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/app/services/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\n\nexport const metadata: Metadata = {\n  title: 'Services | Kozak Home Renovations',\n  description: 'Professional home renovation services including kitchen fitting, bathroom renovations, loft conversions, external wall insulation, and general renovations across Nottinghamshire, Derbyshire & Leicestershire.',\n}\n\nconst services = [\n  {\n    title: 'Kitchen Fitting',\n    description: 'Complete kitchen transformations from design consultation to final installation. We handle everything from plumbing and electrical work to plastering, tiling, and finishing touches.',\n    href: '/services/kitchen-fitting',\n    features: ['Design consultation', 'Complete strip-out', 'Plumbing & electrical', 'Installation & finishing'],\n    icon: '🏠'\n  },\n  {\n    title: 'Bathroom Renovations',\n    description: 'Full bathroom renovations from brickwork up. Transform your bathroom into a modern, functional space with quality fixtures and expert craftsmanship.',\n    href: '/services/bathroom-renovations',\n    features: ['Complete renovation', 'Modern fixtures', 'Waterproofing', 'Tiling & finishing'],\n    icon: '🚿'\n  },\n  {\n    title: 'Loft Conversions',\n    description: 'Transform your unused loft space into valuable living areas. From bedrooms to home offices, we create functional spaces that add value to your home.',\n    href: '/services/loft-conversions',\n    features: ['Space planning', 'Structural work', 'Insulation', 'Finishing & decoration'],\n    icon: '🏗️'\n  },\n  {\n    title: 'External Wall Insulation',\n    description: 'Improve your home&apos;s energy efficiency and exterior appearance with professional external wall insulation systems.',\n    href: '/services/external-wall-insulation',\n    features: ['Energy efficiency', 'Reduced bills', 'Weatherproofing', 'Modern finish'],\n    icon: '🏡'\n  },\n  {\n    title: 'General Renovations',\n    description: 'Comprehensive renovation services including windows, doors, floors, stairs, plastering, tiling, painting, and mold removal.',\n    href: '/services/general-renovations',\n    features: ['Windows & doors', 'Flooring', 'Plastering & tiling', 'Painting & decoration'],\n    icon: '🔨'\n  }\n]\n\nexport default function ServicesPage() {\n  return (\n    <div className=\"py-16\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-5xl font-heading font-bold text-kozak-charcoal mb-6\">\n            Our Services\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            From complete home renovations to specialized installations, we deliver exceptional craftsmanship \n            across all aspects of home improvement. Every project is managed from start to finish with \n            uncompromising attention to quality and detail.\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"space-y-16\">\n          {services.map((service, index) => (\n            <div key={index} className={`flex flex-col lg:flex-row items-center gap-12 ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>\n              <div className=\"flex-1\">\n                <div className=\"text-6xl mb-6\">{service.icon}</div>\n                <h2 className=\"text-3xl font-heading font-bold text-kozak-charcoal mb-4\">\n                  {service.title}\n                </h2>\n                <p className=\"text-lg text-gray-600 mb-6\">\n                  {service.description}\n                </p>\n                <ul className=\"space-y-2 mb-8\">\n                  {service.features.map((feature, featureIndex) => (\n                    <li key={featureIndex} className=\"flex items-center text-gray-700\">\n                      <span className=\"text-kozak-orange mr-3\">✓</span>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n                <Link href={service.href} className=\"btn-primary\">\n                  Learn More\n                </Link>\n              </div>\n              <div className=\"flex-1\">\n                {/* Placeholder for service image */}\n                <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg flex items-center justify-center\">\n                  <div className=\"text-center text-gray-500\">\n                    <div className=\"text-4xl mb-2\">📷</div>\n                    <div className=\"text-lg font-medium\">{service.title}</div>\n                    <div className=\"text-sm\">Image placeholder</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"mt-20 text-center bg-gray-50 rounded-lg p-12\">\n          <h2 className=\"text-3xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Ready to Start Your Project?\n          </h2>\n          <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Contact us today for a free consultation and quote. We&apos;ll discuss your vision and\n            provide expert advice on how to bring it to life.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/contact\" className=\"btn-primary\">\n              Get Free Quote\n            </Link>\n            <a href=\"tel:07849768183\" className=\"btn-secondary\">\n              Call: 07849768183\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEA,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAuB;YAAsB;YAAyB;SAA2B;QAC5G,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAuB;YAAmB;YAAiB;SAAqB;QAC3F,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAkB;YAAmB;YAAc;SAAyB;QACvF,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAqB;YAAiB;YAAmB;SAAgB;QACpF,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAmB;YAAY;YAAuB;SAAwB;QACzF,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuE;;;;;;sCAGrF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAQzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAAgB,WAAW,CAAC,8CAA8C,EAAE,QAAQ,MAAM,IAAI,wBAAwB,IAAI;;8CACzH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAiB,QAAQ,IAAI;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;oDAAsB,WAAU;;sEAC/B,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;wDACxC;;mDAFM;;;;;;;;;;sDAMb,8OAAC,uKAAI;4CAAC,MAAM,QAAQ,IAAI;4CAAE,WAAU;sDAAc;;;;;;;;;;;;8CAIpD,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAuB,QAAQ,KAAK;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;2BA3BvB;;;;;;;;;;8BAoCd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uKAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAc;;;;;;8CAG9C,8OAAC;oCAAE,MAAK;oCAAkB,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhE", "debugId": null}}]}