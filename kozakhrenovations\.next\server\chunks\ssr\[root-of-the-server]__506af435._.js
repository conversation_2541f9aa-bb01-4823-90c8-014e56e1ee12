module.exports=[72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},20238,a=>{"use strict";a.s(["default",()=>e]);var b=a.i(87924),c=a.i(38246),d=a.i(72131);function e(){let[a,e]=(0,d.useState)(!1);return(0,b.jsx)("header",{className:"bg-white shadow-md sticky top-0 z-50",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,b.jsx)(c.default,{href:"/",className:"flex items-center",children:(0,b.jsxs)("div",{className:"text-2xl font-heading font-bold text-kozak-charcoal",children:["Kozak ",(0,b.jsx)("span",{className:"text-kozak-orange",children:"Home Renovations"})]})}),(0,b.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,b.jsx)(c.default,{href:"/",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Home"}),(0,b.jsxs)("div",{className:"relative group",children:[(0,b.jsx)(c.default,{href:"/services",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Services"}),(0,b.jsx)("div",{className:"absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200",children:(0,b.jsxs)("div",{className:"py-2",children:[(0,b.jsx)(c.default,{href:"/services/kitchen-fitting",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"Kitchen Fitting"}),(0,b.jsx)(c.default,{href:"/services/bathroom-renovations",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"Bathroom Renovations"}),(0,b.jsx)(c.default,{href:"/services/loft-conversions",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"Loft Conversions"}),(0,b.jsx)(c.default,{href:"/services/external-wall-insulation",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"External Wall Insulation"}),(0,b.jsx)(c.default,{href:"/services/general-renovations",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"General Renovations"})]})})]}),(0,b.jsx)(c.default,{href:"/our-work",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Our Work"}),(0,b.jsx)(c.default,{href:"/about",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"About"}),(0,b.jsx)(c.default,{href:"/testimonials",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Testimonials"}),(0,b.jsx)(c.default,{href:"/contact",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Contact"}),(0,b.jsx)(c.default,{href:"/blog",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Blog"})]}),(0,b.jsx)("div",{className:"hidden md:block",children:(0,b.jsx)(c.default,{href:"/contact",className:"btn-primary",children:"Get Free Quote"})}),(0,b.jsx)("button",{className:"md:hidden",onClick:()=>e(!a),children:(0,b.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),a&&(0,b.jsx)("div",{className:"md:hidden",children:(0,b.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:[(0,b.jsx)(c.default,{href:"/",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Home"}),(0,b.jsx)(c.default,{href:"/services",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Services"}),(0,b.jsx)(c.default,{href:"/our-work",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Our Work"}),(0,b.jsx)(c.default,{href:"/about",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"About"}),(0,b.jsx)(c.default,{href:"/testimonials",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Testimonials"}),(0,b.jsx)(c.default,{href:"/contact",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Contact"}),(0,b.jsx)(c.default,{href:"/blog",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Blog"}),(0,b.jsx)("div",{className:"px-3 py-2",children:(0,b.jsx)(c.default,{href:"/contact",className:"btn-primary block text-center",children:"Get Free Quote"})})]})})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__506af435._.js.map