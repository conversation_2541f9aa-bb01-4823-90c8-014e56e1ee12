import Link from 'next/link'

// Placeholder gallery items - replace with actual project images
const galleryItems = [
  {
    id: 1,
    title: 'Modern Kitchen Renovation',
    category: 'Kitchen',
    image: '/placeholder-kitchen.jpg',
    alt: 'Modern grey shaker kitchen renovation'
  },
  {
    id: 2,
    title: 'Luxury Bathroom Transformation',
    category: 'Bathroom',
    image: '/placeholder-bathroom.jpg',
    alt: 'Luxury bathroom renovation with modern fixtures'
  },
  {
    id: 3,
    title: 'Loft Conversion',
    category: 'Loft',
    image: '/placeholder-loft.jpg',
    alt: 'Spacious loft conversion with natural light'
  },
  {
    id: 4,
    title: 'External Wall Insulation',
    category: 'Insulation',
    image: '/placeholder-insulation.jpg',
    alt: 'External wall insulation installation'
  },
  {
    id: 5,
    title: 'Complete Home Renovation',
    category: 'General',
    image: '/placeholder-general.jpg',
    alt: 'Complete home renovation project'
  },
  {
    id: 6,
    title: 'Custom Flooring Installation',
    category: 'General',
    image: '/placeholder-flooring.jpg',
    alt: 'Custom hardwood flooring installation'
  }
]

export default function GalleryPreview() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4">
            Our Recent Work
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Take a look at some of our recent projects. Each one showcases our commitment 
            to quality craftsmanship and attention to detail.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {galleryItems.map((item) => (
            <div key={item.id} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                {/* Placeholder for actual images */}
                <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="text-2xl mb-2">📷</div>
                    <div className="text-sm font-medium">{item.title}</div>
                    <div className="text-xs">{item.category}</div>
                  </div>
                </div>
                <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h3 className="font-semibold">{item.title}</h3>
                  <p className="text-sm">{item.category}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Link href="/our-work" className="btn-primary">
            View Full Gallery
          </Link>
        </div>
      </div>
    </section>
  )
}
