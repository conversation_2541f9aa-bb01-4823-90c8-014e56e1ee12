1:"$Sreact.fragment"
2:I[2971,["/_next/static/chunks/d68e1aed4acf5fc6.js"],"default"]
3:I[39756,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"default"]
4:I[37457,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"default"]
5:I[22016,["/_next/static/chunks/d68e1aed4acf5fc6.js"],"default"]
d:I[68027,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"default"]
:HL["/_next/static/chunks/3f0f0e08f89b1f85.css","style"]
0:{"P":null,"b":"UU5vzS83kPQmlcJFUXCBh","p":"","c":["","_not-found"],"i":false,"f":[[["",{"children":["/_not-found",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/3f0f0e08f89b1f85.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","script","script-0",{"src":"/_next/static/chunks/d68e1aed4acf5fc6.js","async":true,"nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"min-h-screen flex flex-col antialiased","children":[["$","$L2",null,{}],["$","main",null,{"className":"flex-grow","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"bg-kozak-charcoal text-white","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-8","children":[["$","div",null,{"className":"col-span-1 md:col-span-2","children":[["$","div",null,{"className":"text-2xl font-heading font-bold mb-4","children":["Kozak ",["$","span",null,{"className":"text-kozak-orange","children":"Home Renovations"}]]}],["$","p",null,{"className":"text-gray-300 mb-4","children":"Master craftsmanship in every detail. Professional home renovations across Nottinghamshire, Derbyshire, and Leicestershire."}],["$","div",null,{"className":"space-y-2 text-gray-300","children":[["$","p",null,{"children":"📞 07849768183"}],["$","p",null,{"children":"✉️ <EMAIL>"}],["$","p",null,{"children":"🕒 Monday - Friday: 8:00 AM - 8:00 PM"}],["$","p",null,{"children":"📍 Serving Nottinghamshire, Derbyshire & Leicestershire"}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-heading font-semibold mb-4","children":"Services"}],["$","ul",null,{"className":"space-y-2 text-gray-300","children":[["$","li",null,{"children":["$","$L5",null,{"href":"/services/kitchen-fitting","className":"hover:text-kozak-orange transition-colors","children":"Kitchen Fitting"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/services/bathroom-renovations","className":"hover:text-kozak-orange transition-colors","children":"Bathroom Renovations"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/services/loft-conversions","className":"hover:text-kozak-orange transition-colors","children":"Loft Conversions"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/services/external-wall-insulation","className":"hover:text-kozak-orange transition-colors","children":"External Wall Insulation"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/services/general-renovations","className":"hover:text-kozak-orange transition-colors","children":"General Renovations"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-heading font-semibold mb-4","children":"Quick Links"}],["$","ul",null,{"className":"space-y-2 text-gray-300","children":[["$","li",null,{"children":["$","$L5",null,{"href":"/our-work","className":"hover:text-kozak-orange transition-colors","children":"Our Work"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/about","className":"hover:text-kozak-orange transition-colors","children":"About Us"}]}],"$L6","$L7","$L8"]}]]}]]}],"$L9"]}]}]]}]}]]}],{"children":["/_not-found","$La",{"children":["__PAGE__","$Lb",{},null,false]},null,false]},null,false],"$Lc",false]],"m":"$undefined","G":["$d","$undefined"],"s":false,"S":true}
e:I[97367,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"OutletBoundary"]
10:I[11533,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"AsyncMetadataOutlet"]
12:I[97367,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"ViewportBoundary"]
14:I[97367,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"MetadataBoundary"]
15:"$Sreact.suspense"
6:["$","li",null,{"children":["$","$L5",null,{"href":"/testimonials","className":"hover:text-kozak-orange transition-colors","children":"Testimonials"}]}]
7:["$","li",null,{"children":["$","$L5",null,{"href":"/contact","className":"hover:text-kozak-orange transition-colors","children":"Contact Us"}]}]
8:["$","li",null,{"children":["$","$L5",null,{"href":"/blog","className":"hover:text-kozak-orange transition-colors","children":"Blog"}]}]
9:["$","div",null,{"className":"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400","children":[["$","p",null,{"children":"© 2024 Kozak Home Renovations. All rights reserved."}],["$","p",null,{"className":"mt-2","children":"Professional home improvements with traditional craftsmanship values."}]]}]
a:["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
b:["$","$1","c",{"children":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:1:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]]}]}]],null,["$","$Le",null,{"children":["$Lf",["$","$L10",null,{"promise":"$@11"}]]}]]}]
c:["$","$1","h",{"children":[["$","meta",null,{"name":"robots","content":"noindex"}],[["$","$L12",null,{"children":"$L13"}],null],["$","$L14",null,{"children":["$","div",null,{"hidden":true,"children":["$","$15",null,{"fallback":null,"children":"$L16"}]}]}]]}]
13:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
f:null
17:I[27201,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/7dd66bdf8a7e5707.js"],"IconMark"]
11:{"metadata":[["$","title","0",{"children":"Kozak Home Renovations | Expert Home Improvements in Nottinghamshire, Derbyshire & Leicestershire"}],["$","meta","1",{"name":"description","content":"Professional home renovations by Marcin Kozak. Specializing in kitchen fitting, bathroom renovations, loft conversions, and external wall insulation across Nottinghamshire, Derbyshire & Leicestershire."}],["$","meta","2",{"name":"keywords","content":"home renovations, kitchen fitting, bathroom renovation, loft conversion, external wall insulation, Nottinghamshire, Derbyshire, Leicestershire"}],["$","link","3",{"rel":"icon","href":"/favicon.ico?favicon.0b3bf435.ico","sizes":"256x256","type":"image/x-icon"}],["$","$L17","4",{}]],"error":null,"digest":"$undefined"}
16:"$11:metadata"
