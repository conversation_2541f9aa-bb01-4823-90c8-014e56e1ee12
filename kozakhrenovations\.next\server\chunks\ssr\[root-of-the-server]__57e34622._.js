module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},29432,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},77341,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],77341);var b,c=a.i(84513);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(21751),a.i(75164),a.i(18970),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(30106),h=a.i(71717);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(20460);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},41763,a=>{"use strict";a.s(["normalizeAppPath",()=>c],41763);var b=a.i(32885);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},54451,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(21751);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},62212,a=>{a.n(a.i(66114))},15731,(a,b,c)=>{},6921,a=>{"use strict";a.s(["default",()=>d,"metadata",()=>c]);var b=a.i(7997);let c={title:"Contact Us | Kozak Home Renovations",description:"Get in touch with Kozak Home Renovations for your free consultation. Serving Nottinghamshire, Derbyshire & Leicestershire. Call 07849768183 or use our contact form."};function d(){return(0,b.jsx)("div",{className:"py-16",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"text-center mb-16",children:[(0,b.jsx)("h1",{className:"text-4xl md:text-5xl font-heading font-bold text-kozak-charcoal mb-6",children:"Contact Us"}),(0,b.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Ready to transform your home? Get in touch today for your free consultation and quote. We're here to discuss your vision and provide expert advice on bringing it to life."})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,b.jsx)("h2",{className:"text-2xl font-heading font-bold text-kozak-charcoal mb-6",children:"Get Your Free Quote"}),(0,b.jsxs)("form",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),(0,b.jsx)("input",{type:"text",id:"firstName",name:"firstName",required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),(0,b.jsx)("input",{type:"text",id:"lastName",name:"lastName",required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,b.jsx)("input",{type:"email",id:"email",name:"email",required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),(0,b.jsx)("input",{type:"tel",id:"phone",name:"phone",required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-2",children:"Service Required *"}),(0,b.jsxs)("select",{id:"service",name:"service",required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent",children:[(0,b.jsx)("option",{value:"",children:"Please select a service"}),(0,b.jsx)("option",{value:"kitchen",children:"Kitchen Fitting"}),(0,b.jsx)("option",{value:"bathroom",children:"Bathroom Renovation"}),(0,b.jsx)("option",{value:"loft",children:"Loft Conversion"}),(0,b.jsx)("option",{value:"insulation",children:"External Wall Insulation"}),(0,b.jsx)("option",{value:"general",children:"General Renovations"}),(0,b.jsx)("option",{value:"consultation",children:"General Consultation"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Project Details"}),(0,b.jsx)("textarea",{id:"message",name:"message",rows:5,placeholder:"Please describe your project, timeline, and any specific requirements...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"})]}),(0,b.jsx)("button",{type:"submit",className:"w-full btn-primary text-lg py-4",children:"Send Message"})]})]}),(0,b.jsxs)("div",{className:"space-y-8",children:[(0,b.jsxs)("div",{className:"bg-kozak-charcoal text-white rounded-lg p-8",children:[(0,b.jsx)("h2",{className:"text-2xl font-heading font-bold mb-6",children:"Get In Touch Directly"}),(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"text-kozak-orange text-2xl mr-4",children:"📞"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"font-semibold",children:"Phone"}),(0,b.jsx)("a",{href:"tel:07849768183",className:"text-gray-300 hover:text-kozak-orange transition-colors",children:"07849768183"})]})]}),(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"text-kozak-orange text-2xl mr-4",children:"✉️"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"font-semibold",children:"Email"}),(0,b.jsx)("a",{href:"mailto:<EMAIL>",className:"text-gray-300 hover:text-kozak-orange transition-colors",children:"<EMAIL>"})]})]}),(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"text-kozak-orange text-2xl mr-4",children:"🕒"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"font-semibold",children:"Business Hours"}),(0,b.jsx)("div",{className:"text-gray-300",children:"Monday - Friday"}),(0,b.jsx)("div",{className:"text-gray-300",children:"8:00 AM - 8:00 PM"})]})]}),(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"text-kozak-orange text-2xl mr-4",children:"📍"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"font-semibold",children:"Service Areas"}),(0,b.jsx)("div",{className:"text-gray-300",children:"Nottinghamshire"}),(0,b.jsx)("div",{className:"text-gray-300",children:"Derbyshire"}),(0,b.jsx)("div",{className:"text-gray-300",children:"Leicestershire"})]})]})]})]}),(0,b.jsxs)("div",{className:"bg-gray-50 rounded-lg p-8",children:[(0,b.jsx)("h3",{className:"text-xl font-heading font-bold text-kozak-charcoal mb-4",children:"Why Choose Kozak Home Renovations?"}),(0,b.jsxs)("ul",{className:"space-y-3",children:[(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-3",children:"✓"}),(0,b.jsx)("span",{className:"text-gray-700",children:"Free, no-obligation consultations"})]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-3",children:"✓"}),(0,b.jsx)("span",{className:"text-gray-700",children:"Transparent, upfront pricing"})]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-3",children:"✓"}),(0,b.jsx)("span",{className:"text-gray-700",children:"Fully insured and qualified"})]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-3",children:"✓"}),(0,b.jsx)("span",{className:"text-gray-700",children:"Project management from start to finish"})]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-3",children:"✓"}),(0,b.jsx)("span",{className:"text-gray-700",children:"Quality craftsmanship guaranteed"})]})]})]}),(0,b.jsxs)("div",{className:"bg-kozak-orange text-white rounded-lg p-6 text-center",children:[(0,b.jsx)("h3",{className:"text-lg font-heading font-bold mb-2",children:"Need Urgent Advice?"}),(0,b.jsx)("p",{className:"mb-4 opacity-90",children:"For urgent renovation queries or emergency repairs"}),(0,b.jsx)("a",{href:"tel:07849768183",className:"bg-white text-kozak-orange hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-colors inline-block",children:"Call Now: 07849768183"})]})]})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__57e34622._.js.map