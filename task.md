# Website Build Tasks for Kozak Home Renovations

## Phase 1: Setup & Foundation

- [ ] Finalise and purchase domain name (e.g., `kozakrenovations.co.uk`).
- [ ] Set up hosting environment.
- [ ] Feed the master prompt to the AI agent to generate the initial site structure and pages.
- [ ] Implement the core design based on the logo's colour palette (<PERSON> Grey, Orange, White).
- [ ] Integrate the company logo into the website header and footer.

## Phase 2: Content Population

- [ ] Write and populate content for the Homepage.
- [ ] Write and populate content for the main Services overview page.
- [ ] Write detailed descriptions, processes, and lists for each individual service sub-page:
    - [ ] Kitchen Fitting
    - [ ] Bathroom Renovations
    - [ ] Loft Conversions
    - [ ] External Wall Insulation
    - [ ] General Renovations
- [ ] Write the "About Us" page content, telling <PERSON><PERSON>'s story.
- [ ] Populate the "Testimonials" page with client feedback.
- [ ] Create the "Contact Us" page with a functional form and correct details.
- [ ] Write and publish the 3 initial blog posts.

## Phase 3: Media & Assets

- [ ] Gather and process high-resolution photos for all services.
- [ ] Create "Before & After" image pairs for the gallery.
- [ ] Upload and structure the filterable gallery on the "Our Work" page.
- [ ] Obtain a professional headshot of <PERSON><PERSON> for the "About Us" page.

## Phase 4: SEO & Optimisation

- [ ] Perform keyword research for the primary service area.
- [ ] Write unique meta titles and descriptions for all pages.
- [ ] Add descriptive alt text to all images.
- [ ] Ensure logical H1/H2/H3 heading structure throughout the site.
- [ ] Implement LocalBusiness schema markup.
- [ ] Set up Google Analytics and Google Search Console.
- [ ] Test website speed and optimise for performance.
- [ ] Test for mobile responsiveness across multiple devices.

## Phase 5: Launch

- [ ] Final review and proofread of all content.
- [ ] Test the contact form to ensure it's working.
- [ ] Point the domain name to the new website.
- [ ] Submit the sitemap to Google Search Console.
- [ ] Set up a redirect from the old domain (`mkredevelopments.uk`) to the new one.