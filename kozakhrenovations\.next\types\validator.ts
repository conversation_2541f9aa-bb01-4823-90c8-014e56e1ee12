// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../src/app/about/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/about">> = Specific
  const handler = {} as typeof import("../../src/app/about/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/blog/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/blog">> = Specific
  const handler = {} as typeof import("../../src/app/blog/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/contact/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/contact">> = Specific
  const handler = {} as typeof import("../../src/app/contact/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/our-work/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/our-work">> = Specific
  const handler = {} as typeof import("../../src/app/our-work/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/services/bathroom-renovations/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/services/bathroom-renovations">> = Specific
  const handler = {} as typeof import("../../src/app/services/bathroom-renovations/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/services/external-wall-insulation/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/services/external-wall-insulation">> = Specific
  const handler = {} as typeof import("../../src/app/services/external-wall-insulation/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/services/general-renovations/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/services/general-renovations">> = Specific
  const handler = {} as typeof import("../../src/app/services/general-renovations/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/services/kitchen-fitting/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/services/kitchen-fitting">> = Specific
  const handler = {} as typeof import("../../src/app/services/kitchen-fitting/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/services/loft-conversions/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/services/loft-conversions">> = Specific
  const handler = {} as typeof import("../../src/app/services/loft-conversions/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/services/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/services">> = Specific
  const handler = {} as typeof import("../../src/app/services/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/testimonials/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/testimonials">> = Specific
  const handler = {} as typeof import("../../src/app/testimonials/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}







// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
