import type { Metadata } from "next";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

export const metadata: Metadata = {
  title: "<PERSON>zak Home Renovations | Expert Home Improvements in Nottinghamshire, Derbyshire & Leicestershire",
  description: "Professional home renovations by <PERSON><PERSON>. Specializing in kitchen fitting, bathroom renovations, loft conversions, and external wall insulation across Nottinghamshire, Derbyshire & Leicestershire.",
  keywords: "home renovations, kitchen fitting, bathroom renovation, loft conversion, external wall insulation, Nottinghamshire, Derbyshire, Leicestershire",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className="min-h-screen flex flex-col antialiased"
        suppressHydrationWarning={true}
      >
        <Header />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
