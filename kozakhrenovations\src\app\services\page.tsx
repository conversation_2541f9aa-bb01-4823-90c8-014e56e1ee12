import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Services | Kozak Home Renovations',
  description: 'Professional home renovation services including kitchen fitting, bathroom renovations, loft conversions, external wall insulation, and general renovations across Nottinghamshire, Derbyshire & Leicestershire.',
}

const services = [
  {
    title: 'Kitchen Fitting',
    description: 'Complete kitchen transformations from design consultation to final installation. We handle everything from plumbing and electrical work to plastering, tiling, and finishing touches.',
    href: '/services/kitchen-fitting',
    features: ['Design consultation', 'Complete strip-out', 'Plumbing & electrical', 'Installation & finishing'],
    icon: '🏠'
  },
  {
    title: 'Bathroom Renovations',
    description: 'Full bathroom renovations from brickwork up. Transform your bathroom into a modern, functional space with quality fixtures and expert craftsmanship.',
    href: '/services/bathroom-renovations',
    features: ['Complete renovation', 'Modern fixtures', 'Waterproofing', 'Tiling & finishing'],
    icon: '🚿'
  },
  {
    title: 'Loft Conversions',
    description: 'Transform your unused loft space into valuable living areas. From bedrooms to home offices, we create functional spaces that add value to your home.',
    href: '/services/loft-conversions',
    features: ['Space planning', 'Structural work', 'Insulation', 'Finishing & decoration'],
    icon: '🏗️'
  },
  {
    title: 'External Wall Insulation',
    description: 'Improve your home&apos;s energy efficiency and exterior appearance with professional external wall insulation systems.',
    href: '/services/external-wall-insulation',
    features: ['Energy efficiency', 'Reduced bills', 'Weatherproofing', 'Modern finish'],
    icon: '🏡'
  },
  {
    title: 'General Renovations',
    description: 'Comprehensive renovation services including windows, doors, floors, stairs, plastering, tiling, painting, and mold removal.',
    href: '/services/general-renovations',
    features: ['Windows & doors', 'Flooring', 'Plastering & tiling', 'Painting & decoration'],
    icon: '🔨'
  }
]

export default function ServicesPage() {
  return (
    <div className="py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-heading font-bold text-kozak-charcoal mb-6">
            Our Services
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From complete home renovations to specialized installations, we deliver exceptional craftsmanship 
            across all aspects of home improvement. Every project is managed from start to finish with 
            uncompromising attention to quality and detail.
          </p>
        </div>

        {/* Services Grid */}
        <div className="space-y-16">
          {services.map((service, index) => (
            <div key={index} className={`flex flex-col lg:flex-row items-center gap-12 ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>
              <div className="flex-1">
                <div className="text-6xl mb-6">{service.icon}</div>
                <h2 className="text-3xl font-heading font-bold text-kozak-charcoal mb-4">
                  {service.title}
                </h2>
                <p className="text-lg text-gray-600 mb-6">
                  {service.description}
                </p>
                <ul className="space-y-2 mb-8">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-700">
                      <span className="text-kozak-orange mr-3">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <Link href={service.href} className="btn-primary">
                  Learn More
                </Link>
              </div>
              <div className="flex-1">
                {/* Placeholder for service image */}
                <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="text-4xl mb-2">📷</div>
                    <div className="text-lg font-medium">{service.title}</div>
                    <div className="text-sm">Image placeholder</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center bg-gray-50 rounded-lg p-12">
          <h2 className="text-3xl font-heading font-bold text-kozak-charcoal mb-4">
            Ready to Start Your Project?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Contact us today for a free consultation and quote. We&apos;ll discuss your vision and
            provide expert advice on how to bring it to life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn-primary">
              Get Free Quote
            </Link>
            <a href="tel:07849768183" className="btn-secondary">
              Call: 07849768183
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
