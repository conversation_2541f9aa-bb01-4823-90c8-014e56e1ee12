import Link from 'next/link'

export default function TestimonialsPage() {
  // Placeholder testimonials data - will be replaced with Supabase data
  const testimonials = [
    {
      id: 1,
      author_name: "<PERSON>",
      location: "Nottingham",
      rating: 5,
      content: "<PERSON><PERSON> transformed our outdated kitchen into a stunning modern space. His attention to detail and quality of work exceeded our expectations. The project was completed on time and within budget. We couldn't be happier with the results!",
      project_type: "Kitchen Renovation"
    },
    {
      id: 2,
      author_name: "<PERSON>",
      location: "Derby",
      rating: 5,
      content: "Outstanding bathroom renovation! <PERSON><PERSON> was professional, reliable, and the quality of workmanship was exceptional. He kept us informed throughout the process and left everything spotless. Highly recommended!",
      project_type: "Bathroom Renovation"
    },
    {
      id: 3,
      author_name: "David & Claire R.",
      location: "Leicester",
      rating: 5,
      content: "We had our loft converted into a beautiful bedroom and office space. <PERSON><PERSON> handled everything from planning permission to the final touches. The transformation is incredible and has added real value to our home.",
      project_type: "Loft Conversion"
    },
    {
      id: 4,
      author_name: "<PERSON>",
      location: "Mansfield",
      rating: 5,
      content: "Excellent external wall insulation work. Our energy bills have reduced significantly and the house looks fantastic. <PERSON><PERSON> was knowledgeable, professional, and completed the work with minimal disruption.",
      project_type: "External Wall Insulation"
    },
    {
      id: 5,
      author_name: "<PERSON> & <PERSON> W.",
      location: "Chesterfield",
      rating: 5,
      content: "Complete home renovation including new flooring, windows, and decorating throughout. Marcin's attention to detail is second to none. Every room looks perfect and the quality is outstanding.",
      project_type: "General Renovation"
    },
    {
      id: 6,
      author_name: "Rachel Green",
      location: "Loughborough",
      rating: 5,
      content: "Professional, reliable, and excellent value for money. <PERSON>in fitted our new kitchen and the result is better than we ever imagined. We've already recommended him to friends and family.",
      project_type: "Kitchen Fitting"
    }
  ]

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-2xl ${i < rating ? 'text-kozak-orange' : 'text-gray-300'}`}>
        ★
      </span>
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-kozak-charcoal to-gray-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-6">
              Customer Testimonials
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200">
              What Our Customers Say About Us
            </p>
            <p className="text-lg max-w-3xl mx-auto text-gray-300">
              Don&apos;t just take our word for it. Read what our satisfied customers across 
              Nottinghamshire, Derbyshire, and Leicestershire have to say about their 
              renovation experience with Kozak Home Renovations.
            </p>
          </div>
        </div>
      </section>

      {/* Testimonials Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex justify-center mb-4">
                  <div className="flex space-x-1">
                    {renderStars(testimonial.rating)}
                  </div>
                </div>
                <blockquote className="text-gray-600 mb-6 italic">
                  &ldquo;{testimonial.content}&rdquo;
                </blockquote>
                <div className="border-t pt-4">
                  <div className="font-semibold text-kozak-charcoal">
                    {testimonial.author_name}
                  </div>
                  <div className="text-sm text-gray-500">
                    {testimonial.location}
                  </div>
                  <div className="text-sm text-kozak-orange font-medium mt-1">
                    {testimonial.project_type}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-heading font-bold text-kozak-charcoal mb-8">
            Our Track Record
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-4xl font-bold text-kozak-orange mb-2">100+</div>
              <div className="text-gray-600">Projects Completed</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-kozak-orange mb-2">5★</div>
              <div className="text-gray-600">Average Rating</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-kozak-orange mb-2">98%</div>
              <div className="text-gray-600">Customer Satisfaction</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-kozak-orange mb-2">3</div>
              <div className="text-gray-600">Counties Served</div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Submission CTA */}
      <section className="py-16 bg-kozak-orange bg-opacity-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-heading font-bold text-kozak-charcoal mb-6">
            Share Your Experience
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Have you worked with Kozak Home Renovations? We&apos;d love to hear about your experience 
            and share your story with future customers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn-primary">
              Leave a Review
            </Link>
            <Link href="/our-work" className="btn-secondary">
              View Our Work
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-kozak-charcoal text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-heading font-bold mb-4">
            Ready to Join Our Happy Customers?
          </h2>
          <p className="text-xl mb-8 text-gray-200">
            Get your free consultation today and discover why our customers recommend us
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn-primary">
              Get Free Quote
            </Link>
            <a href="tel:07849768183" className="btn-secondary bg-transparent border-2 border-white hover:bg-white hover:text-kozak-charcoal">
              Call: 07849768183
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
