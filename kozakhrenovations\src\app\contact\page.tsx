import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Contact Us | Kozak Home Renovations',
  description: 'Get in touch with Kozak Home Renovations for your free consultation. Serving Nottinghamshire, Derbyshire & Leicestershire. Call 07849768183 or use our contact form.',
}

export default function ContactPage() {
  return (
    <div className="py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-heading font-bold text-kozak-charcoal mb-6">
            Contact Us
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Ready to transform your home? Get in touch today for your free consultation and quote. 
            We&apos;re here to discuss your vision and provide expert advice on bringing it to life.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-heading font-bold text-kozak-charcoal mb-6">
              Get Your Free Quote
            </h2>
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"
                  />
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Required *
                </label>
                <select
                  id="service"
                  name="service"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"
                >
                  <option value="">Please select a service</option>
                  <option value="kitchen">Kitchen Fitting</option>
                  <option value="bathroom">Bathroom Renovation</option>
                  <option value="loft">Loft Conversion</option>
                  <option value="insulation">External Wall Insulation</option>
                  <option value="general">General Renovations</option>
                  <option value="consultation">General Consultation</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Project Details
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={5}
                  placeholder="Please describe your project, timeline, and any specific requirements..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kozak-orange focus:border-transparent"
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full btn-primary text-lg py-4"
              >
                Send Message
              </button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            {/* Direct Contact */}
            <div className="bg-kozak-charcoal text-white rounded-lg p-8">
              <h2 className="text-2xl font-heading font-bold mb-6">
                Get In Touch Directly
              </h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="text-kozak-orange text-2xl mr-4">📞</div>
                  <div>
                    <div className="font-semibold">Phone</div>
                    <a href="tel:07849768183" className="text-gray-300 hover:text-kozak-orange transition-colors">
                      07849768183
                    </a>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="text-kozak-orange text-2xl mr-4">✉️</div>
                  <div>
                    <div className="font-semibold">Email</div>
                    <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-kozak-orange transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="text-kozak-orange text-2xl mr-4">🕒</div>
                  <div>
                    <div className="font-semibold">Business Hours</div>
                    <div className="text-gray-300">Monday - Friday</div>
                    <div className="text-gray-300">8:00 AM - 8:00 PM</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="text-kozak-orange text-2xl mr-4">📍</div>
                  <div>
                    <div className="font-semibold">Service Areas</div>
                    <div className="text-gray-300">Nottinghamshire</div>
                    <div className="text-gray-300">Derbyshire</div>
                    <div className="text-gray-300">Leicestershire</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Why Choose Us */}
            <div className="bg-gray-50 rounded-lg p-8">
              <h3 className="text-xl font-heading font-bold text-kozak-charcoal mb-4">
                Why Choose Kozak Home Renovations?
              </h3>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <span className="text-kozak-orange mr-3">✓</span>
                  <span className="text-gray-700">Free, no-obligation consultations</span>
                </li>
                <li className="flex items-center">
                  <span className="text-kozak-orange mr-3">✓</span>
                  <span className="text-gray-700">Transparent, upfront pricing</span>
                </li>
                <li className="flex items-center">
                  <span className="text-kozak-orange mr-3">✓</span>
                  <span className="text-gray-700">Fully insured and qualified</span>
                </li>
                <li className="flex items-center">
                  <span className="text-kozak-orange mr-3">✓</span>
                  <span className="text-gray-700">Project management from start to finish</span>
                </li>
                <li className="flex items-center">
                  <span className="text-kozak-orange mr-3">✓</span>
                  <span className="text-gray-700">Quality craftsmanship guaranteed</span>
                </li>
              </ul>
            </div>

            {/* Emergency Contact */}
            <div className="bg-kozak-orange text-white rounded-lg p-6 text-center">
              <h3 className="text-lg font-heading font-bold mb-2">
                Need Urgent Advice?
              </h3>
              <p className="mb-4 opacity-90">
                For urgent renovation queries or emergency repairs
              </p>
              <a href="tel:07849768183" className="bg-white text-kozak-orange hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-colors inline-block">
                Call Now: 07849768183
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
