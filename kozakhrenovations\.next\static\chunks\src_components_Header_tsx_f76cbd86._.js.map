{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { useState } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white shadow-md sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <Image\n              src=\"/kozak-logo.png\"\n              alt=\"Kozak Home Renovations Logo\"\n              width={50}\n              height={50}\n              className=\"w-12 h-12\"\n            />\n            <div className=\"text-2xl font-heading font-bold text-kozak-charcoal\">\n              Kozak <span className=\"text-kozak-orange\">Home Renovations</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link href=\"/\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Home\n            </Link>\n            <div className=\"relative group\">\n              <Link href=\"/services\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n                Services\n              </Link>\n              <div className=\"absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                <div className=\"py-2\">\n                  <Link href=\"/services/kitchen-fitting\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Kitchen Fitting\n                  </Link>\n                  <Link href=\"/services/bathroom-renovations\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Bathroom Renovations\n                  </Link>\n                  <Link href=\"/services/loft-conversions\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Loft Conversions\n                  </Link>\n                  <Link href=\"/services/external-wall-insulation\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    External Wall Insulation\n                  </Link>\n                  <Link href=\"/services/general-renovations\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    General Renovations\n                  </Link>\n                </div>\n              </div>\n            </div>\n            <Link href=\"/our-work\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Our Work\n            </Link>\n            <Link href=\"/about\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              About\n            </Link>\n            <Link href=\"/testimonials\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Testimonials\n            </Link>\n            <Link href=\"/contact\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Contact\n            </Link>\n            <Link href=\"/blog\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Blog\n            </Link>\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Link href=\"/contact\" className=\"btn-primary\">\n              Get Free Quote\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n              <Link href=\"/\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Home\n              </Link>\n              <Link href=\"/services\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Services\n              </Link>\n              <Link href=\"/our-work\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Our Work\n              </Link>\n              <Link href=\"/about\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                About\n              </Link>\n              <Link href=\"/testimonials\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Testimonials\n              </Link>\n              <Link href=\"/contact\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Contact\n              </Link>\n              <Link href=\"/blog\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Blog\n              </Link>\n              <div className=\"px-3 py-2\">\n                <Link href=\"/contact\" className=\"btn-primary block text-center\">\n                  Get Free Quote\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,0KAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,2IAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;wCAAsD;sDAC7D,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;;;;;;;sCAK9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0KAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgE;;;;;;8CAGzF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,0KAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAgE;;;;;;sDAGjG,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,0KAAI;wDAAC,MAAK;wDAA4B,WAAU;kEAA+D;;;;;;kEAGhH,6LAAC,0KAAI;wDAAC,MAAK;wDAAiC,WAAU;kEAA+D;;;;;;kEAGrH,6LAAC,0KAAI;wDAAC,MAAK;wDAA6B,WAAU;kEAA+D;;;;;;kEAGjH,6LAAC,0KAAI;wDAAC,MAAK;wDAAqC,WAAU;kEAA+D;;;;;;kEAGzH,6LAAC,0KAAI;wDAAC,MAAK;wDAAgC,WAAU;kEAA+D;;;;;;;;;;;;;;;;;;;;;;;8CAM1H,6LAAC,0KAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAgE;;;;;;8CAGjG,6LAAC,0KAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAgE;;;;;;8CAG9F,6LAAC,0KAAI;oCAAC,MAAK;oCAAgB,WAAU;8CAAgE;;;;;;8CAGrG,6LAAC,0KAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgE;;;;;;8CAGhG,6LAAC,0KAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAgE;;;;;;;;;;;;sCAM/F,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0KAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAc;;;;;;;;;;;sCAMhD,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE9B,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAM1E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA8D;;;;;;0CAGvF,6LAAC,0KAAI;gCAAC,MAAK;gCAAY,WAAU;0CAA8D;;;;;;0CAG/F,6LAAC,0KAAI;gCAAC,MAAK;gCAAY,WAAU;0CAA8D;;;;;;0CAG/F,6LAAC,0KAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA8D;;;;;;0CAG5F,6LAAC,0KAAI;gCAAC,MAAK;gCAAgB,WAAU;0CAA8D;;;;;;0CAGnG,6LAAC,0KAAI;gCAAC,MAAK;gCAAW,WAAU;0CAA8D;;;;;;0CAG9F,6LAAC,0KAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAA8D;;;;;;0CAG3F,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,0KAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhF;GAzHwB;KAAA", "debugId": null}}]}