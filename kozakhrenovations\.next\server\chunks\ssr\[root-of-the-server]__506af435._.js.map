{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/node_modules/next/src/client/components/handle-isr-error.tsx", "turbopack:///[project]/node_modules/next/src/client/components/builtin/global-error.tsx", "turbopack:///[project]/src/components/Header.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n", "'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n", "'use client'\n\nimport Link from 'next/link'\nimport { useState } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white shadow-md sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-heading font-bold text-kozak-charcoal\">\n              Kozak <span className=\"text-kozak-orange\">Home Renovations</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link href=\"/\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Home\n            </Link>\n            <div className=\"relative group\">\n              <Link href=\"/services\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n                Services\n              </Link>\n              <div className=\"absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                <div className=\"py-2\">\n                  <Link href=\"/services/kitchen-fitting\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Kitchen Fitting\n                  </Link>\n                  <Link href=\"/services/bathroom-renovations\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Bathroom Renovations\n                  </Link>\n                  <Link href=\"/services/loft-conversions\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Loft Conversions\n                  </Link>\n                  <Link href=\"/services/external-wall-insulation\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    External Wall Insulation\n                  </Link>\n                  <Link href=\"/services/general-renovations\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    General Renovations\n                  </Link>\n                </div>\n              </div>\n            </div>\n            <Link href=\"/our-work\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Our Work\n            </Link>\n            <Link href=\"/about\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              About\n            </Link>\n            <Link href=\"/testimonials\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Testimonials\n            </Link>\n            <Link href=\"/contact\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Contact\n            </Link>\n            <Link href=\"/blog\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Blog\n            </Link>\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Link href=\"/contact\" className=\"btn-primary\">\n              Get Free Quote\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n              <Link href=\"/\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Home\n              </Link>\n              <Link href=\"/services\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Services\n              </Link>\n              <Link href=\"/our-work\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Our Work\n              </Link>\n              <Link href=\"/about\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                About\n              </Link>\n              <Link href=\"/testimonials\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Testimonials\n              </Link>\n              <Link href=\"/contact\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Contact\n              </Link>\n              <Link href=\"/blog\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Blog\n              </Link>\n              <div className=\"px-3 py-2\">\n                <Link href=\"/contact\" className=\"btn-primary block text-center\">\n                  Get Free Quote\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": ["module", "exports", "require", "vendored", "React", "AppRouterContext", "ReactServerDOMTurbopackClient", "HooksClientContext", "ReactDOM", "ServerInsertedHtml", "process", "env", "NEXT_RUNTIME", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "ReactJsxRuntime", "HandleISRError", "workAsyncStorage", "window", "undefined", "error", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p"], "mappings": "6CAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK,8BCF9BJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACE,gBAAgB,+BCFvCL,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEG,6BAA6B,+BCFtDN,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACI,kBAAkB,+BCFzCP,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEK,QAAQ,+BCFjCR,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACM,kBAAkB,+sBCwBjCT,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,gCC1BjCF,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEa,eAAe,wGCQxBC,iBAAAA,qCAAAA,KAVhB,IAAMC,EAGEhB,EAAQ,CAAA,CAAA,IAAA,GACRgB,MAHN,OAAOC,GAGe,CAMjB,EALDC,KAJc,EASJH,EAAe,CAAyB,EAAzB,GAAA,CAAEI,OAAK,CAAkB,CAAzB,EAC7B,GAAIH,EAAkB,CACpB,IAAMI,EAAQJ,EAAiBK,QAAQ,GACvC,GAAID,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOE,YAAAA,AAAY,IAAIF,CAAJ,OAAIA,KAAAA,EAAAA,EAAOG,kBAAAA,AAAkB,EAElD,CAFoD,KACpDC,QAAQL,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,+TCgCA,OAFA,AACA,GACA,qCAAA,GAD2C,uBAjDZ,CAAA,CAAA,IAAA,GAEzBM,EAAS,CACbN,EA6C8E,IA7CvE,CAELO,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA8BA,EAzBA,SAA4B,AAyBbC,AAzBNA,CAA4C,EAAzB,GAAA,OAAEnB,CAAK,CAAkB,CAAzB,EACpBoB,EAA6BpB,QAAAA,KAAAA,EAAAA,EAAOoB,MAAM,CAChD,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAACC,CADH,MACGA,CAAKC,GAAG,2BACP,GAAA,EAAA,GAAA,EAACC,OAAAA,CAAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAACC,OAAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC5B,EAAAA,cAAc,CAAA,CAACI,MAAOA,IACvB,CAAA,EAAA,EAAA,GAAA,EAACyB,MAAAA,CAAIC,MAAOpB,EAAON,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACyB,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,IAAA,EAACE,KAAAA,CAAGD,MAAOpB,EAAOQ,IAAI,WAAE,wBACAM,EAAS,SAAW,SAAS,8CACvBtB,OAAO8B,QAAQ,CAACC,QAAQ,CAAC,YAAU,IAC9DT,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAS,GAAA,EAAA,EAATA,CAAS,EAACU,IAAAA,CAAEJ,GAAZN,GAAmBd,EAAOQ,IAAI,UAAI,WAAUM,IAAgB,eAMzE,yRC9CA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,GAAM,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,GAAS,GAE7C,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,gDAChB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDAEb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,IAAI,UAAU,6BACvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gEAAsD,SAC7D,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6BAAoB,0BAK9C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,IAAI,UAAU,yEAAgE,SAGzF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,YAAY,UAAU,yEAAgE,aAGjG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+JACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,4BAA4B,UAAU,wEAA+D,oBAGhH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,iCAAiC,UAAU,wEAA+D,yBAGrH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,6BAA6B,UAAU,wEAA+D,qBAGjH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,qCAAqC,UAAU,wEAA+D,6BAGzH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,gCAAgC,UAAU,wEAA+D,gCAM1H,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,YAAY,UAAU,yEAAgE,aAGjG,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,SAAS,UAAU,yEAAgE,UAG9F,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,gBAAgB,UAAU,yEAAgE,iBAGrG,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,yEAAgE,YAGhG,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,QAAQ,UAAU,yEAAgE,YAM/F,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,uBAAc,qBAMhD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,UAAU,YACV,QAAS,IAAM,EAAc,CAAC,YAE9B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,QAAQ,YAAY,OAAO,wBAC9D,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,mCAM1E,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,IAAI,UAAU,uEAA8D,SAGvF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,YAAY,UAAU,uEAA8D,aAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,YAAY,UAAU,uEAA8D,aAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,SAAS,UAAU,uEAA8D,UAG5F,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,gBAAgB,UAAU,uEAA8D,iBAGnG,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,uEAA8D,YAG9F,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,QAAQ,UAAU,uEAA8D,SAG3F,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,yCAAgC,8BAUhF", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}