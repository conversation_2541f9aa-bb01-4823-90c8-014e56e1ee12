import Link from 'next/link'

export default function BlogPage() {
  // Placeholder blog posts - will be replaced with CMS or Supabase data
  const blogPosts = [
    {
      id: 1,
      title: "10 Kitchen Design Trends for 2024",
      excerpt: "Discover the latest kitchen design trends that are transforming homes across the UK. From sustainable materials to smart storage solutions.",
      date: "2024-01-15",
      category: "Kitchen Design",
      readTime: "5 min read",
      image: "kitchen-trends"
    },
    {
      id: 2,
      title: "The Complete Guide to Bathroom Renovations",
      excerpt: "Everything you need to know about planning and executing a successful bathroom renovation, from initial design to final installation.",
      date: "2024-01-10",
      category: "Bathroom Renovation",
      readTime: "8 min read",
      image: "bathroom-guide"
    },
    {
      id: 3,
      title: "Loft Conversion: Planning Permission vs Permitted Development",
      excerpt: "Understanding when you need planning permission for your loft conversion and how to navigate the building regulations process.",
      date: "2024-01-05",
      category: "Loft Conversions",
      readTime: "6 min read",
      image: "loft-planning"
    },
    {
      id: 4,
      title: "External Wall Insulation: Benefits and Installation Process",
      excerpt: "Learn how external wall insulation can reduce your energy bills and improve your home's appearance with our comprehensive guide.",
      date: "2023-12-28",
      category: "Energy Efficiency",
      readTime: "7 min read",
      image: "wall-insulation"
    },
    {
      id: 5,
      title: "Choosing the Right Flooring for Your Home",
      excerpt: "Compare different flooring options including laminate, LVT, and engineered wood to find the perfect solution for each room.",
      date: "2023-12-20",
      category: "Flooring",
      readTime: "5 min read",
      image: "flooring-guide"
    },
    {
      id: 6,
      title: "Home Renovation on a Budget: Tips and Tricks",
      excerpt: "Maximize your renovation budget with these expert tips on where to save and where to invest for the best return on investment.",
      date: "2023-12-15",
      category: "Budget Tips",
      readTime: "6 min read",
      image: "budget-renovation"
    }
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-GB', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-kozak-charcoal to-gray-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-6">
              Renovation Blog
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200">
              Expert Tips, Guides & Industry Insights
            </p>
            <p className="text-lg max-w-3xl mx-auto text-gray-300">
              Stay informed with the latest renovation trends, expert advice, and practical 
              guides to help you make the best decisions for your home improvement projects.
            </p>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="aspect-[16/9] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="text-2xl mb-2">📝</div>
                    <div className="text-sm font-medium">{post.title}</div>
                    <div className="text-xs">{post.category}</div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-kozak-orange font-medium">
                      {post.category}
                    </span>
                    <span className="text-sm text-gray-500">
                      {post.readTime}
                    </span>
                  </div>
                  <h2 className="text-xl font-heading font-semibold text-kozak-charcoal mb-3 hover:text-kozak-orange transition-colors">
                    <Link href={`/blog/${post.id}`}>
                      {post.title}
                    </Link>
                  </h2>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {formatDate(post.date)}
                    </span>
                    <Link 
                      href={`/blog/${post.id}`}
                      className="text-kozak-orange hover:text-kozak-charcoal font-medium text-sm transition-colors"
                    >
                      Read More →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-heading font-bold text-kozak-charcoal mb-8 text-center">
            Browse by Category
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['Kitchen Design', 'Bathroom Renovation', 'Loft Conversions', 'Energy Efficiency', 'Flooring', 'Budget Tips', 'Planning & Permits', 'Maintenance'].map((category) => (
              <Link
                key={category}
                href={`/blog/category/${category.toLowerCase().replace(/\s+/g, '-')}`}
                className="bg-gray-50 hover:bg-kozak-orange hover:text-white text-center py-4 px-3 rounded-lg transition-colors duration-200 text-sm font-medium"
              >
                {category}
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-kozak-orange bg-opacity-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-heading font-bold text-kozak-charcoal mb-6">
            Stay Updated
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Subscribe to our newsletter for the latest renovation tips, project showcases, 
            and exclusive offers delivered straight to your inbox.
          </p>
          <div className="max-w-md mx-auto">
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-kozak-orange focus:border-transparent"
              />
              <button className="btn-primary whitespace-nowrap">
                Subscribe
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-3">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-kozak-charcoal text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-heading font-bold mb-4">
            Ready to Start Your Renovation?
          </h2>
          <p className="text-xl mb-8 text-gray-200">
            Get expert advice and a free consultation for your home improvement project
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn-primary">
              Get Free Consultation
            </Link>
            <a href="tel:07849768183" className="btn-secondary bg-transparent border-2 border-white hover:bg-white hover:text-kozak-charcoal">
              Call: 07849768183
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
