{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/Hero.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function Hero() {\n  return (\n    <section className=\"relative bg-gradient-to-r from-kozak-charcoal to-gray-800 text-white\">\n      <div className=\"absolute inset-0 bg-black opacity-50\"></div>\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-heading font-bold mb-6\">\n            Master Craftsmanship in Every Detail\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 text-gray-200\">\n            Your Vision, Built to Last\n          </p>\n          <p className=\"text-lg mb-10 max-w-3xl mx-auto text-gray-300\">\n            Professional home renovations across Nottinghamshire, Derbyshire, and Leicestershire. \n            From kitchen fitting to loft conversions, we deliver uncompromising quality with traditional craftsmanship values.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/contact\" className=\"btn-primary text-lg px-8 py-4\">\n              Get Your Free Quote\n            </Link>\n            <Link href=\"/our-work\" className=\"btn-secondary text-lg px-8 py-4\">\n              View Our Work\n            </Link>\n          </div>\n        </div>\n      </div>\n      \n      {/* Background placeholder - replace with actual hero image */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"w-full h-full bg-gradient-to-br from-kozak-charcoal via-gray-700 to-kozak-orange opacity-20\"></div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uKAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgC;;;;;;8CAGhE,8OAAC,uKAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/ServicesOverview.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nconst services = [\n  {\n    title: 'Kitchen Fitting',\n    description: 'Complete kitchen transformations from design to installation',\n    href: '/services/kitchen-fitting',\n    icon: '🏠'\n  },\n  {\n    title: 'Bathroom Renovations',\n    description: 'Full bathroom renovations from brickwork up',\n    href: '/services/bathroom-renovations',\n    icon: '🚿'\n  },\n  {\n    title: 'Loft Conversions',\n    description: 'Transform your loft into valuable living space',\n    href: '/services/loft-conversions',\n    icon: '🏗️'\n  },\n  {\n    title: 'External Wall Insulation',\n    description: 'Improve energy efficiency and exterior finish',\n    href: '/services/external-wall-insulation',\n    icon: '🏡'\n  },\n  {\n    title: 'General Renovations',\n    description: 'Windows, doors, floors, stairs, plastering, tiling, painting',\n    href: '/services/general-renovations',\n    icon: '🔨'\n  }\n]\n\nexport default function ServicesOverview() {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Our Services\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            From complete renovations to specialized installations, we deliver exceptional craftsmanship \n            across all aspects of home improvement.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <Link\n              key={index}\n              href={service.href}\n              className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 group\"\n            >\n              <div className=\"text-4xl mb-4\">{service.icon}</div>\n              <h3 className=\"text-xl font-heading font-semibold text-kozak-charcoal mb-3 group-hover:text-kozak-orange transition-colors\">\n                {service.title}\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                {service.description}\n              </p>\n              <div className=\"text-kozak-orange font-semibold group-hover:underline\">\n                Learn More →\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuE;;;;;;sCAGrF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,uKAAI;4BAEH,MAAM,QAAQ,IAAI;4BAClB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CAAiB,QAAQ,IAAI;;;;;;8CAC5C,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;8CAAwD;;;;;;;2BAXlE;;;;;;;;;;;;;;;;;;;;;AAoBnB", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/WhyChooseUs.tsx"], "sourcesContent": ["const features = [\n  {\n    title: 'Uncompromising Quality',\n    description: 'Every project is completed to the highest standards with attention to detail that sets us apart.',\n    icon: '⭐'\n  },\n  {\n    title: 'Fully Managed Projects',\n    description: 'From initial consultation to final handover, we manage every aspect of your renovation.',\n    icon: '📋'\n  },\n  {\n    title: 'Expert & Insured',\n    description: 'Fully qualified craftsman with comprehensive insurance for your peace of mind.',\n    icon: '🛡️'\n  },\n  {\n    title: 'Transparent Pricing',\n    description: 'Clear, upfront pricing with no hidden costs. You know exactly what you\\'re paying for.',\n    icon: '💰'\n  }\n]\n\nexport default function WhyChooseUs() {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Why <PERSON><PERSON> Home Renovations?\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            With years of experience and a commitment to excellence, we deliver renovations \n            that exceed expectations and stand the test of time.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-5xl mb-4\">{feature.icon}</div>\n              <h3 className=\"text-xl font-heading font-semibold text-kozak-charcoal mb-3\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuE;;;;;;sCAGrF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CAAiB,QAAQ,IAAI;;;;;;8CAC5C,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BANd;;;;;;;;;;;;;;;;;;;;;AActB", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/GalleryPreview.tsx"], "sourcesContent": ["import Link from 'next/link'\n\n// Placeholder gallery items - replace with actual project images\nconst galleryItems = [\n  {\n    id: 1,\n    title: 'Modern Kitchen Renovation',\n    category: 'Kitchen',\n    image: '/placeholder-kitchen.jpg',\n    alt: 'Modern grey shaker kitchen renovation'\n  },\n  {\n    id: 2,\n    title: 'Luxury Bathroom Transformation',\n    category: 'Bathroom',\n    image: '/placeholder-bathroom.jpg',\n    alt: 'Luxury bathroom renovation with modern fixtures'\n  },\n  {\n    id: 3,\n    title: 'Loft Conversion',\n    category: 'Loft',\n    image: '/placeholder-loft.jpg',\n    alt: 'Spacious loft conversion with natural light'\n  },\n  {\n    id: 4,\n    title: 'External Wall Insulation',\n    category: 'Insulation',\n    image: '/placeholder-insulation.jpg',\n    alt: 'External wall insulation installation'\n  },\n  {\n    id: 5,\n    title: 'Complete Home Renovation',\n    category: 'General',\n    image: '/placeholder-general.jpg',\n    alt: 'Complete home renovation project'\n  },\n  {\n    id: 6,\n    title: 'Custom Flooring Installation',\n    category: 'General',\n    image: '/placeholder-flooring.jpg',\n    alt: 'Custom hardwood flooring installation'\n  }\n]\n\nexport default function GalleryPreview() {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Our Recent Work\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Take a look at some of our recent projects. Each one showcases our commitment \n            to quality craftsmanship and attention to detail.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          {galleryItems.map((item) => (\n            <div key={item.id} className=\"group cursor-pointer\">\n              <div className=\"relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\">\n                {/* Placeholder for actual images */}\n                <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n                  <div className=\"text-center text-gray-500\">\n                    <div className=\"text-2xl mb-2\">📷</div>\n                    <div className=\"text-sm font-medium\">{item.title}</div>\n                    <div className=\"text-xs\">{item.category}</div>\n                  </div>\n                </div>\n                <div className=\"absolute inset-0 bg-black opacity-0 group-hover:opacity-30 transition-opacity duration-300\"></div>\n                <div className=\"absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <h3 className=\"font-semibold\">{item.title}</h3>\n                  <p className=\"text-sm\">{item.category}</p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"text-center\">\n          <Link href=\"/our-work\" className=\"btn-primary\">\n            View Full Gallery\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,iEAAiE;AACjE,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,KAAK;IACP;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuE;;;;;;sCAGrF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4BAAkB,WAAU;sCAC3B,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAuB,KAAK,KAAK;;;;;;8DAChD,8OAAC;oDAAI,WAAU;8DAAW,KAAK,QAAQ;;;;;;;;;;;;;;;;;kDAG3C,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiB,KAAK,KAAK;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAAW,KAAK,QAAQ;;;;;;;;;;;;;;;;;;2BAbjC,KAAK,EAAE;;;;;;;;;;8BAoBrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uKAAI;wBAAC,MAAK;wBAAY,WAAU;kCAAc;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/TestimonialSnippet.tsx"], "sourcesContent": ["export default function TestimonialSnippet() {\n  return (\n    <section className=\"py-16 bg-kozak-charcoal text-white\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"mb-8\">\n          <div className=\"text-kozak-orange text-6xl mb-4\">&ldquo;</div>\n          <blockquote className=\"text-xl md:text-2xl font-light italic mb-6\">\n            <PERSON><PERSON> transformed our outdated kitchen into a stunning modern space. His attention to detail\n            and quality of work exceeded our expectations. The project was completed on time and within budget.\n            We couldn&apos;t be happier with the results!\n          </blockquote>\n          <div className=\"text-lg\">\n            <div className=\"font-semibold\"><PERSON> & <PERSON>.</div>\n            <div className=\"text-gray-300\">Kitchen Renovation, Nottingham</div>\n          </div>\n        </div>\n        \n        <div className=\"flex justify-center mb-6\">\n          <div className=\"flex space-x-1\">\n            {[...Array(5)].map((_, i) => (\n              <span key={i} className=\"text-kozak-orange text-2xl\">★</span>\n            ))}\n          </div>\n        </div>\n\n        <p className=\"text-gray-300 mb-8\">\n          Join our growing list of satisfied customers across Nottinghamshire, Derbyshire, and Leicestershire.\n        </p>\n\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <a href=\"/testimonials\" className=\"btn-primary\">\n            Read More Reviews\n          </a>\n          <a href=\"/contact\" className=\"btn-secondary bg-transparent border-2 border-white hover:bg-white hover:text-kozak-charcoal\">\n            Share Your Experience\n          </a>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAkC;;;;;;sCACjD,8OAAC;4BAAW,WAAU;sCAA6C;;;;;;sCAKnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAInC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAa,WAAU;0CAA6B;+BAA1C;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAIlC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,MAAK;4BAAgB,WAAU;sCAAc;;;;;;sCAGhD,8OAAC;4BAAE,MAAK;4BAAW,WAAU;sCAA8F;;;;;;;;;;;;;;;;;;;;;;;AAOrI", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/CTABanner.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function CTABanner() {\n  return (\n    <section className=\"py-16 bg-kozak-orange text-white\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h2 className=\"text-3xl md:text-4xl font-heading font-bold mb-4\">\n          Ready to Transform Your Home?\n        </h2>\n        <p className=\"text-xl mb-8 opacity-90\">\n          Get your free consultation today and discover how we can bring your vision to life \n          with our expert craftsmanship and attention to detail.\n        </p>\n        \n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-8\">\n          <Link href=\"/contact\" className=\"bg-white text-kozak-orange hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 text-lg\">\n            Get Free Quote\n          </Link>\n          <a href=\"tel:07849768183\" className=\"bg-transparent border-2 border-white hover:bg-white hover:text-kozak-orange font-semibold py-4 px-8 rounded-lg transition-colors duration-200 text-lg\">\n            Call Now: 07849768183\n          </a>\n        </div>\n\n        <div className=\"text-sm opacity-75\">\n          <p>Serving Nottinghamshire, Derbyshire & Leicestershire</p>\n          <p>Monday - Friday: 8:00 AM - 8:00 PM</p>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAmD;;;;;;8BAGjE,8OAAC;oBAAE,WAAU;8BAA0B;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uKAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAyH;;;;;;sCAGzJ,8OAAC;4BAAE,MAAK;4BAAkB,WAAU;sCAAwJ;;;;;;;;;;;;8BAK9L,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/app/page.tsx"], "sourcesContent": ["import Hero from '@/components/Hero'\nimport ServicesOverview from '@/components/ServicesOverview'\nimport WhyChooseUs from '@/components/WhyChooseUs'\nimport GalleryPreview from '@/components/GalleryPreview'\nimport TestimonialSnippet from '@/components/TestimonialSnippet'\nimport CTABanner from '@/components/CTABanner'\n\nexport default function Home() {\n  return (\n    <div>\n      <Hero />\n      <ServicesOverview />\n      <WhyChooseUs />\n      <GalleryPreview />\n      <TestimonialSnippet />\n      <CTABanner />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,qIAAI;;;;;0BACL,8OAAC,iJAAgB;;;;;0BACjB,8OAAC,4IAAW;;;;;0BACZ,8OAAC,+IAAc;;;;;0BACf,8OAAC,mJAAkB;;;;;0BACnB,8OAAC,0IAAS;;;;;;;;;;;AAGhB", "debugId": null}}]}