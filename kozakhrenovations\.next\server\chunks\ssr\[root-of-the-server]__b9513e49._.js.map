{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/app/our-work/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: 'Our Work - Project Gallery | Kozak Home Renovations',\n  description: 'View our portfolio of completed home renovation projects including kitchens, bathrooms, loft conversions, and more across Nottinghamshire, Derbyshire & Leicestershire.',\n}\n\n// Placeholder project data - replace with actual Supabase data\nconst projects = [\n  {\n    id: 1,\n    title: 'Modern Kitchen Renovation',\n    category: 'kitchen',\n    location: 'Nottingham',\n    description: 'Complete kitchen transformation with modern units and appliances',\n    beforeImage: '/placeholder-kitchen-before.jpg',\n    afterImage: '/placeholder-kitchen-after.jpg',\n    featured: true\n  },\n  {\n    id: 2,\n    title: 'Luxury Bathroom Suite',\n    category: 'bathroom',\n    location: 'Derby',\n    description: 'Full bathroom renovation with premium fixtures and tiling',\n    beforeImage: '/placeholder-bathroom-before.jpg',\n    afterImage: '/placeholder-bathroom-after.jpg',\n    featured: true\n  },\n  {\n    id: 3,\n    title: 'Loft Conversion',\n    category: 'loft',\n    location: 'Leicester',\n    description: 'Spacious loft conversion creating additional bedroom space',\n    beforeImage: '/placeholder-loft-before.jpg',\n    afterImage: '/placeholder-loft-after.jpg',\n    featured: false\n  },\n  {\n    id: 4,\n    title: 'External Wall Insulation',\n    category: 'insulation',\n    location: 'Nottingham',\n    description: 'Energy-efficient external wall insulation with modern render finish',\n    beforeImage: '/placeholder-insulation-before.jpg',\n    afterImage: '/placeholder-insulation-after.jpg',\n    featured: false\n  },\n  {\n    id: 5,\n    title: 'Complete Home Renovation',\n    category: 'general',\n    location: 'Derby',\n    description: 'Full house renovation including flooring, decoration, and fixtures',\n    beforeImage: '/placeholder-general-before.jpg',\n    afterImage: '/placeholder-general-after.jpg',\n    featured: true\n  },\n  {\n    id: 6,\n    title: 'Victorian Kitchen Restoration',\n    category: 'kitchen',\n    location: 'Leicester',\n    description: 'Period kitchen restoration maintaining character while adding modern functionality',\n    beforeImage: '/placeholder-kitchen2-before.jpg',\n    afterImage: '/placeholder-kitchen2-after.jpg',\n    featured: false\n  }\n]\n\nconst categories = [\n  { id: 'all', name: 'All Projects', count: projects.length },\n  { id: 'kitchen', name: 'Kitchens', count: projects.filter(p => p.category === 'kitchen').length },\n  { id: 'bathroom', name: 'Bathrooms', count: projects.filter(p => p.category === 'bathroom').length },\n  { id: 'loft', name: 'Lofts', count: projects.filter(p => p.category === 'loft').length },\n  { id: 'insulation', name: 'Insulation', count: projects.filter(p => p.category === 'insulation').length },\n  { id: 'general', name: 'General', count: projects.filter(p => p.category === 'general').length }\n]\n\nexport default function OurWorkPage() {\n  return (\n    <div className=\"py-16\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-5xl font-heading font-bold text-kozak-charcoal mb-6\">\n            Our Work\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Explore our portfolio of completed projects. Each renovation showcases our commitment \n            to quality craftsmanship and attention to detail. Click on any project to see the \n            before and after transformation.\n          </p>\n        </div>\n\n        {/* Filter Buttons */}\n        <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n          {categories.map((category) => (\n            <button\n              key={category.id}\n              className=\"px-6 py-3 rounded-lg font-semibold transition-colors duration-200 bg-gray-200 text-kozak-charcoal hover:bg-kozak-orange hover:text-white\"\n            >\n              {category.name} ({category.count})\n            </button>\n          ))}\n        </div>\n\n        {/* Projects Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {projects.map((project) => (\n            <div key={project.id} className=\"group cursor-pointer\">\n              <div className=\"relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\">\n                {/* Placeholder for before/after slider */}\n                <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center relative\">\n                  <div className=\"text-center text-gray-500\">\n                    <div className=\"text-3xl mb-2\">🏠</div>\n                    <div className=\"text-sm font-medium\">Before & After</div>\n                    <div className=\"text-xs\">{project.title}</div>\n                  </div>\n                  \n                  {/* Featured badge */}\n                  {project.featured && (\n                    <div className=\"absolute top-4 left-4 bg-kozak-orange text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      Featured\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-sm font-medium text-kozak-orange uppercase tracking-wide\">\n                      {project.category}\n                    </span>\n                    <span className=\"text-sm text-gray-500\">{project.location}</span>\n                  </div>\n                  <h3 className=\"text-xl font-heading font-semibold text-kozak-charcoal mb-2 group-hover:text-kozak-orange transition-colors\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-gray-600 text-sm\">\n                    {project.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center bg-gray-50 rounded-lg p-12\">\n          <h2 className=\"text-3xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Ready to Start Your Project?\n          </h2>\n          <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Every project starts with a conversation. Contact us today to discuss your vision \n            and see how we can transform your space with the same quality and attention to detail.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a href=\"/contact\" className=\"btn-primary\">\n              Get Free Quote\n            </a>\n            <a href=\"tel:07849768183\" className=\"btn-secondary\">\n              Call: 07849768183\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEA,+DAA+D;AAC/D,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,MAAM;QAAgB,OAAO,SAAS,MAAM;IAAC;IAC1D;QAAE,IAAI;QAAW,MAAM;QAAY,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,WAAW,MAAM;IAAC;IAChG;QAAE,IAAI;QAAY,MAAM;QAAa,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;IAAC;IACnG;QAAE,IAAI;QAAQ,MAAM;QAAS,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;IAAC;IACvF;QAAE,IAAI;QAAc,MAAM;QAAc,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,cAAc,MAAM;IAAC;IACxG;QAAE,IAAI;QAAW,MAAM;QAAW,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,WAAW,MAAM;IAAC;CAChG;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuE;;;;;;sCAGrF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAQzD,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4BAEC,WAAU;;gCAET,SAAS,IAAI;gCAAC;gCAAG,SAAS,KAAK;gCAAC;;2BAH5B,SAAS,EAAE;;;;;;;;;;8BAStB,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAAqB,WAAU;sCAC9B,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,8OAAC;wDAAI,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;4CAIxC,QAAQ,QAAQ,kBACf,8OAAC;gDAAI,WAAU;0DAAgG;;;;;;;;;;;;kDAMnH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;kEAEnB,8OAAC;wDAAK,WAAU;kEAAyB,QAAQ,QAAQ;;;;;;;;;;;;0DAE3D,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2BA7BlB,QAAQ,EAAE;;;;;;;;;;8BAsCxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAc;;;;;;8CAG3C,8OAAC;oCAAE,MAAK;oCAAkB,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhE", "debugId": null}}]}