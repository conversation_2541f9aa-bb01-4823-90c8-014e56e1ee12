module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},29432,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},77341,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],77341);var b,c=a.i(84513);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(21751),a.i(75164),a.i(18970),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(30106),h=a.i(71717);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(20460);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},41763,a=>{"use strict";a.s(["normalizeAppPath",()=>c],41763);var b=a.i(32885);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},54451,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(21751);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},62212,a=>{a.n(a.i(66114))},49901,(a,b,c)=>{},60168,a=>{"use strict";a.s(["default",()=>m],60168);var b=a.i(7997),c=a.i(97647);function d(){return(0,b.jsxs)("section",{className:"relative bg-gradient-to-r from-kozak-charcoal to-gray-800 text-white",children:[(0,b.jsx)("div",{className:"absolute inset-0 bg-black opacity-50"}),(0,b.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h1",{className:"text-4xl md:text-6xl font-heading font-bold mb-6",children:"Master Craftsmanship in Every Detail"}),(0,b.jsx)("p",{className:"text-xl md:text-2xl mb-8 text-gray-200",children:"Your Vision, Built to Last"}),(0,b.jsx)("p",{className:"text-lg mb-10 max-w-3xl mx-auto text-gray-300",children:"Professional home renovations across Nottinghamshire, Derbyshire, and Leicestershire. From kitchen fitting to loft conversions, we deliver uncompromising quality with traditional craftsmanship values."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,b.jsx)(c.default,{href:"/contact",className:"btn-primary text-lg px-8 py-4",children:"Get Your Free Quote"}),(0,b.jsx)(c.default,{href:"/our-work",className:"btn-secondary text-lg px-8 py-4",children:"View Our Work"})]})]})}),(0,b.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,b.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-kozak-charcoal via-gray-700 to-kozak-orange opacity-20"})})]})}let e=[{title:"Kitchen Fitting",description:"Complete kitchen transformations from design to installation",href:"/services/kitchen-fitting",icon:"🏠"},{title:"Bathroom Renovations",description:"Full bathroom renovations from brickwork up",href:"/services/bathroom-renovations",icon:"🚿"},{title:"Loft Conversions",description:"Transform your loft into valuable living space",href:"/services/loft-conversions",icon:"🏗️"},{title:"External Wall Insulation",description:"Improve energy efficiency and exterior finish",href:"/services/external-wall-insulation",icon:"🏡"},{title:"General Renovations",description:"Windows, doors, floors, stairs, plastering, tiling, painting",href:"/services/general-renovations",icon:"🔨"}];function f(){return(0,b.jsx)("section",{className:"py-16 bg-gray-50",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"text-center mb-12",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4",children:"Our Services"}),(0,b.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"From complete renovations to specialized installations, we deliver exceptional craftsmanship across all aspects of home improvement."})]}),(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((a,d)=>(0,b.jsxs)(c.default,{href:a.href,className:"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 group",children:[(0,b.jsx)("div",{className:"text-4xl mb-4",children:a.icon}),(0,b.jsx)("h3",{className:"text-xl font-heading font-semibold text-kozak-charcoal mb-3 group-hover:text-kozak-orange transition-colors",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 mb-4",children:a.description}),(0,b.jsx)("div",{className:"text-kozak-orange font-semibold group-hover:underline",children:"Learn More →"})]},d))})]})})}let g=[{title:"Uncompromising Quality",description:"Every project is completed to the highest standards with attention to detail that sets us apart.",icon:"⭐"},{title:"Fully Managed Projects",description:"From initial consultation to final handover, we manage every aspect of your renovation.",icon:"📋"},{title:"Expert & Insured",description:"Fully qualified craftsman with comprehensive insurance for your peace of mind.",icon:"🛡️"},{title:"Transparent Pricing",description:"Clear, upfront pricing with no hidden costs. You know exactly what you're paying for.",icon:"💰"}];function h(){return(0,b.jsx)("section",{className:"py-16 bg-white",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"text-center mb-12",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4",children:"Why Choose Kozak Home Renovations?"}),(0,b.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"With years of experience and a commitment to excellence, we deliver renovations that exceed expectations and stand the test of time."})]}),(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:g.map((a,c)=>(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-5xl mb-4",children:a.icon}),(0,b.jsx)("h3",{className:"text-xl font-heading font-semibold text-kozak-charcoal mb-3",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600",children:a.description})]},c))})]})})}let i=[{id:1,title:"Modern Kitchen Renovation",category:"Kitchen",image:"/placeholder-kitchen.jpg",alt:"Modern grey shaker kitchen renovation"},{id:2,title:"Luxury Bathroom Transformation",category:"Bathroom",image:"/placeholder-bathroom.jpg",alt:"Luxury bathroom renovation with modern fixtures"},{id:3,title:"Loft Conversion",category:"Loft",image:"/placeholder-loft.jpg",alt:"Spacious loft conversion with natural light"},{id:4,title:"External Wall Insulation",category:"Insulation",image:"/placeholder-insulation.jpg",alt:"External wall insulation installation"},{id:5,title:"Complete Home Renovation",category:"General",image:"/placeholder-general.jpg",alt:"Complete home renovation project"},{id:6,title:"Custom Flooring Installation",category:"General",image:"/placeholder-flooring.jpg",alt:"Custom hardwood flooring installation"}];function j(){return(0,b.jsx)("section",{className:"py-16 bg-gray-50",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"text-center mb-12",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4",children:"Our Recent Work"}),(0,b.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Take a look at some of our recent projects. Each one showcases our commitment to quality craftsmanship and attention to detail."})]}),(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:i.map(a=>(0,b.jsx)("div",{className:"group cursor-pointer",children:(0,b.jsxs)("div",{className:"relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300",children:[(0,b.jsx)("div",{className:"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center",children:(0,b.jsxs)("div",{className:"text-center text-gray-500",children:[(0,b.jsx)("div",{className:"text-2xl mb-2",children:"📷"}),(0,b.jsx)("div",{className:"text-sm font-medium",children:a.title}),(0,b.jsx)("div",{className:"text-xs",children:a.category})]})}),(0,b.jsx)("div",{className:"absolute inset-0 bg-black opacity-0 group-hover:opacity-30 transition-opacity duration-300"}),(0,b.jsxs)("div",{className:"absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,b.jsx)("h3",{className:"font-semibold",children:a.title}),(0,b.jsx)("p",{className:"text-sm",children:a.category})]})]})},a.id))}),(0,b.jsx)("div",{className:"text-center",children:(0,b.jsx)(c.default,{href:"/our-work",className:"btn-primary",children:"View Full Gallery"})})]})})}function k(){return(0,b.jsx)("section",{className:"py-16 bg-kozak-charcoal text-white",children:(0,b.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("div",{className:"text-kozak-orange text-6xl mb-4",children:"“"}),(0,b.jsx)("blockquote",{className:"text-xl md:text-2xl font-light italic mb-6",children:"Marcin transformed our outdated kitchen into a stunning modern space. His attention to detail and quality of work exceeded our expectations. The project was completed on time and within budget. We couldn't be happier with the results!"}),(0,b.jsxs)("div",{className:"text-lg",children:[(0,b.jsx)("div",{className:"font-semibold",children:"Sarah & John M."}),(0,b.jsx)("div",{className:"text-gray-300",children:"Kitchen Renovation, Nottingham"})]})]}),(0,b.jsx)("div",{className:"flex justify-center mb-6",children:(0,b.jsx)("div",{className:"flex space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((a,c)=>(0,b.jsx)("span",{className:"text-kozak-orange text-2xl",children:"★"},c))})}),(0,b.jsx)("p",{className:"text-gray-300 mb-8",children:"Join our growing list of satisfied customers across Nottinghamshire, Derbyshire, and Leicestershire."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,b.jsx)("a",{href:"/testimonials",className:"btn-primary",children:"Read More Reviews"}),(0,b.jsx)("a",{href:"/contact",className:"btn-secondary bg-transparent border-2 border-white hover:bg-white hover:text-kozak-charcoal",children:"Share Your Experience"})]})]})})}function l(){return(0,b.jsx)("section",{className:"py-16 bg-kozak-orange text-white",children:(0,b.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl font-heading font-bold mb-4",children:"Ready to Transform Your Home?"}),(0,b.jsx)("p",{className:"text-xl mb-8 opacity-90",children:"Get your free consultation today and discover how we can bring your vision to life with our expert craftsmanship and attention to detail."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mb-8",children:[(0,b.jsx)(c.default,{href:"/contact",className:"bg-white text-kozak-orange hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 text-lg",children:"Get Free Quote"}),(0,b.jsx)("a",{href:"tel:07849768183",className:"bg-transparent border-2 border-white hover:bg-white hover:text-kozak-orange font-semibold py-4 px-8 rounded-lg transition-colors duration-200 text-lg",children:"Call Now: 07849768183"})]}),(0,b.jsxs)("div",{className:"text-sm opacity-75",children:[(0,b.jsx)("p",{children:"Serving Nottinghamshire, Derbyshire & Leicestershire"}),(0,b.jsx)("p",{children:"Monday - Friday: 8:00 AM - 8:00 PM"})]})]})})}function m(){return(0,b.jsxs)("div",{children:[(0,b.jsx)(d,{}),(0,b.jsx)(f,{}),(0,b.jsx)(h,{}),(0,b.jsx)(j,{}),(0,b.jsx)(k,{}),(0,b.jsx)(l,{})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__5a66d329._.js.map