(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},98183,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return s},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return a}})},95057,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return l},urlObjectKeys:function(){return s}});let n=e.r(90809)._(e.r(98183)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",s=e.pathname||"",l=e.hash||"",c=e.query||"",i=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?i=t+e.host:r&&(i=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(i+=":"+e.port)),c&&"object"==typeof c&&(c=String(n.urlQueryToSearchParams(c)));let u=e.search||c&&"?"+c||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==i?(i="//"+(i||""),s&&"/"!==s[0]&&(s="/"+s)):i||(i=""),l&&"#"!==l[0]&&(l="#"+l),u&&"?"!==u[0]&&(u="?"+u),""+a+i+(s=s.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+l}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return a(e)}},18581,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(71645);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18967,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return x},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return k},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return i},getLocationOrigin:function(){return l},getURL:function(){return c},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return f},stringifyError:function(){return v}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>a.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function c(){let{href:e}=window.location,t=l();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function f(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class x extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class k extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=e.r(18967),o=e.r(52817);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},84508,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},22016,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return m},useLinkStatus:function(){return k}});let n=e.r(90809),o=e.r(43476),a=n._(e.r(71645)),s=e.r(95057),l=e.r(8372),c=e.r(18581),i=e.r(18967),u=e.r(5550);e.r(33525);let f=e.r(91949),d=e.r(73668),h=e.r(99781);e.r(84508);let p=e.r(65165);function x(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function m(e){var t;let r,n,s,[m,k]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:v,as:b,children:j,prefetch:N=null,passHref:P,replace:z,shallow:O,scroll:_,onClick:E,onMouseEnter:S,onTouchStart:T,legacyBehavior:C=!1,onNavigate:w,ref:R,unstable_dynamicOnHover:A,...M}=e;r=j,C&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let L=a.default.useContext(l.AppRouterContext),U=!1!==N,I=!1!==N?null===(t=N)||"auto"===t?p.FetchStrategy.PPR:p.FetchStrategy.Full:p.FetchStrategy.PPR,{href:F,as:B}=a.default.useMemo(()=>{let e=x(v);return{href:e,as:b?x(b):e}},[v,b]);C&&(n=a.default.Children.only(r));let K=C?n&&"object"==typeof n&&n.ref:R,D=a.default.useCallback(e=>(null!==L&&(y.current=(0,f.mountLinkInstance)(e,F,L,I,U,k)),()=>{y.current&&((0,f.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,f.unmountPrefetchableInstance)(e)}),[U,F,L,I,k]),W={ref:(0,c.useMergedRef)(D,K),onClick(e){C||"function"!=typeof E||E(e),C&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,o,s,l){let{nodeName:c}=e.currentTarget;if(!("A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,h.dispatchNavigateAction)(r||t,o?"replace":"push",null==s||s,n.current)})}}(e,F,B,y,z,_,w))},onMouseEnter(e){C||"function"!=typeof S||S(e),C&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&U&&(0,f.onNavigationIntent)(e.currentTarget,!0===A)},onTouchStart:function(e){C||"function"!=typeof T||T(e),C&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&U&&(0,f.onNavigationIntent)(e.currentTarget,!0===A)}};return(0,i.isAbsoluteUrl)(B)?W.href=B:C&&!P&&("a"!==n.type||"href"in n.props)||(W.href=(0,u.addBasePath)(B)),s=C?a.default.cloneElement(n,W):(0,o.jsx)("a",{...M,...W,children:r}),(0,o.jsx)(g.Provider,{value:m,children:s})}let g=(0,a.createContext)(f.IDLE_LINK_STATUS),k=()=>(0,a.useContext)(g);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},2971,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(43476),r=e.i(22016),n=e.i(71645);function o(){let[e,o]=(0,n.useState)(!1);return(0,t.jsx)("header",{className:"bg-white shadow-md sticky top-0 z-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,t.jsx)(r.default,{href:"/",className:"flex items-center",children:(0,t.jsxs)("div",{className:"text-2xl font-heading font-bold text-kozak-charcoal",children:["Kozak ",(0,t.jsx)("span",{className:"text-kozak-orange",children:"Home Renovations"})]})}),(0,t.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,t.jsx)(r.default,{href:"/",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Home"}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)(r.default,{href:"/services",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Services"}),(0,t.jsx)("div",{className:"absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200",children:(0,t.jsxs)("div",{className:"py-2",children:[(0,t.jsx)(r.default,{href:"/services/kitchen-fitting",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"Kitchen Fitting"}),(0,t.jsx)(r.default,{href:"/services/bathroom-renovations",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"Bathroom Renovations"}),(0,t.jsx)(r.default,{href:"/services/loft-conversions",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"Loft Conversions"}),(0,t.jsx)(r.default,{href:"/services/external-wall-insulation",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"External Wall Insulation"}),(0,t.jsx)(r.default,{href:"/services/general-renovations",className:"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50",children:"General Renovations"})]})})]}),(0,t.jsx)(r.default,{href:"/our-work",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Our Work"}),(0,t.jsx)(r.default,{href:"/about",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"About"}),(0,t.jsx)(r.default,{href:"/testimonials",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Testimonials"}),(0,t.jsx)(r.default,{href:"/contact",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Contact"}),(0,t.jsx)(r.default,{href:"/blog",className:"text-kozak-charcoal hover:text-kozak-orange transition-colors",children:"Blog"})]}),(0,t.jsx)("div",{className:"hidden md:block",children:(0,t.jsx)(r.default,{href:"/contact",className:"btn-primary",children:"Get Free Quote"})}),(0,t.jsx)("button",{className:"md:hidden",onClick:()=>o(!e),children:(0,t.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),e&&(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:[(0,t.jsx)(r.default,{href:"/",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Home"}),(0,t.jsx)(r.default,{href:"/services",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Services"}),(0,t.jsx)(r.default,{href:"/our-work",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Our Work"}),(0,t.jsx)(r.default,{href:"/about",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"About"}),(0,t.jsx)(r.default,{href:"/testimonials",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Testimonials"}),(0,t.jsx)(r.default,{href:"/contact",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Contact"}),(0,t.jsx)(r.default,{href:"/blog",className:"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange",children:"Blog"}),(0,t.jsx)("div",{className:"px-3 py-2",children:(0,t.jsx)(r.default,{href:"/contact",className:"btn-primary block text-center",children:"Get Free Quote"})})]})})]})})}}]);