module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},29432,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},77341,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],77341);var b,c=a.i(84513);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(21751),a.i(75164),a.i(18970),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(30106),h=a.i(71717);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(20460);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},41763,a=>{"use strict";a.s(["normalizeAppPath",()=>c],41763);var b=a.i(32885);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},54451,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(21751);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},62212,a=>{a.n(a.i(66114))},58792,(a,b,c)=>{},64361,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(7997),c=a.i(97647);function d(){return(0,b.jsx)("div",{className:"py-16",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"text-center mb-16",children:[(0,b.jsx)("h1",{className:"text-4xl md:text-5xl font-heading font-bold text-kozak-charcoal mb-6",children:"Expert Kitchen Fitting & Design"}),(0,b.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Transform your kitchen into the heart of your home with our complete kitchen fitting service. From initial design consultation to final installation, we handle every aspect with precision and care."})]}),(0,b.jsx)("div",{className:"mb-16",children:(0,b.jsx)("div",{className:"aspect-[16/9] bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg flex items-center justify-center",children:(0,b.jsxs)("div",{className:"text-center text-gray-500",children:[(0,b.jsx)("div",{className:"text-6xl mb-4",children:"🏠"}),(0,b.jsx)("div",{className:"text-2xl font-medium",children:"Kitchen Renovation Showcase"}),(0,b.jsx)("div",{className:"text-lg",children:"Before & After Gallery"})]})})}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-12",children:[(0,b.jsx)("div",{className:"lg:col-span-2",children:(0,b.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,b.jsx)("h2",{className:"text-3xl font-heading font-bold text-kozak-charcoal mb-6",children:"Complete Kitchen Transformation Service"}),(0,b.jsx)("p",{className:"text-gray-600 mb-6",children:"At Kozak Home Renovations, we understand that your kitchen is more than just a place to cook—it's the heart of your home. Our comprehensive kitchen fitting service covers everything from initial design consultation to the final finishing touches, ensuring your new kitchen exceeds your expectations."}),(0,b.jsx)("h3",{className:"text-2xl font-heading font-semibold text-kozak-charcoal mb-4",children:"Our Kitchen Fitting Process"}),(0,b.jsxs)("div",{className:"space-y-6 mb-8",children:[(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)("div",{className:"bg-kozak-orange text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 mt-1",children:"1"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-kozak-charcoal mb-2",children:"Consultation & Design"}),(0,b.jsx)("p",{className:"text-gray-600",children:"We start with a detailed consultation to understand your needs, preferences, and budget. Our design process ensures your new kitchen is both beautiful and functional."})]})]}),(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)("div",{className:"bg-kozak-orange text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 mt-1",children:"2"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-kozak-charcoal mb-2",children:"Preparation & Strip-out"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Safe removal of existing kitchen units, appliances, and fixtures. We protect your home and ensure minimal disruption during this phase."})]})]}),(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)("div",{className:"bg-kozak-orange text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 mt-1",children:"3"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-kozak-charcoal mb-2",children:"Installation & Craftsmanship"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Expert installation of units, worktops, appliances, and fixtures. All plumbing, electrical, and gas work is carried out to the highest standards."})]})]}),(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)("div",{className:"bg-kozak-orange text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 mt-1",children:"4"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-kozak-charcoal mb-2",children:"Finishing Touches & Handover"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Final plastering, tiling, painting, and decoration. We ensure every detail is perfect before handing over your beautiful new kitchen."})]})]})]}),(0,b.jsx)("h3",{className:"text-2xl font-heading font-semibold text-kozak-charcoal mb-4",children:"What's Included in Our Kitchen Fitting Service"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8",children:[(0,b.jsxs)("ul",{className:"space-y-2",children:[(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Design consultation"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Complete strip-out"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Plumbing installation"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Electrical work"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Gas appliance connection"]})]}),(0,b.jsxs)("ul",{className:"space-y-2",children:[(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Unit installation"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Worktop fitting"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Tiling & splashbacks"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Plastering & decoration"]}),(0,b.jsxs)("li",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-kozak-orange mr-2",children:"✓"})," Final finishing"]})]})]})]})}),(0,b.jsxs)("div",{className:"lg:col-span-1",children:[(0,b.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8",children:[(0,b.jsx)("h3",{className:"text-xl font-heading font-semibold text-kozak-charcoal mb-4",children:"Ready to Start Your Kitchen Project?"}),(0,b.jsx)("p",{className:"text-gray-600 mb-6",children:"Contact us today for a free consultation and quote for your kitchen renovation."}),(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsx)(c.default,{href:"/contact",className:"btn-primary w-full text-center block",children:"Get Free Quote"}),(0,b.jsx)("a",{href:"tel:07849768183",className:"btn-secondary w-full text-center block",children:"Call: 07849768183"})]})]}),(0,b.jsxs)("div",{className:"bg-kozak-charcoal text-white rounded-lg p-6",children:[(0,b.jsx)("h3",{className:"text-xl font-heading font-semibold mb-4",children:"Service Areas"}),(0,b.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,b.jsx)("li",{children:"• Nottinghamshire"}),(0,b.jsx)("li",{children:"• Derbyshire"}),(0,b.jsx)("li",{children:"• Leicestershire"})]}),(0,b.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-600",children:(0,b.jsxs)("p",{className:"text-sm text-gray-300",children:[(0,b.jsx)("strong",{children:"Business Hours:"}),(0,b.jsx)("br",{}),"Monday - Friday",(0,b.jsx)("br",{}),"8:00 AM - 8:00 PM"]})})]})]})]}),(0,b.jsxs)("div",{className:"mt-16 text-center bg-kozak-orange text-white rounded-lg p-12",children:[(0,b.jsx)("h2",{className:"text-3xl font-heading font-bold mb-4",children:"Ready to start your kitchen project? Contact us today for a free consultation."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,b.jsx)(c.default,{href:"/contact",className:"bg-white text-kozak-orange hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-colors",children:"Get Free Consultation"}),(0,b.jsx)(c.default,{href:"/our-work",className:"bg-transparent border-2 border-white hover:bg-white hover:text-kozak-orange font-semibold py-3 px-6 rounded-lg transition-colors",children:"View Kitchen Gallery"})]})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__c25baba3._.js.map