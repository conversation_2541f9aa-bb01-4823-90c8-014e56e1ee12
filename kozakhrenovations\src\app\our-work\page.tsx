import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Our Work - Project Gallery | Kozak Home Renovations',
  description: 'View our portfolio of completed home renovation projects including kitchens, bathrooms, loft conversions, and more across Nottinghamshire, Derbyshire & Leicestershire.',
}

// Placeholder project data - replace with actual Supabase data
const projects = [
  {
    id: 1,
    title: 'Modern Kitchen Renovation',
    category: 'kitchen',
    location: 'Nottingham',
    description: 'Complete kitchen transformation with modern units and appliances',
    beforeImage: '/placeholder-kitchen-before.jpg',
    afterImage: '/placeholder-kitchen-after.jpg',
    featured: true
  },
  {
    id: 2,
    title: 'Luxury Bathroom Suite',
    category: 'bathroom',
    location: 'Derby',
    description: 'Full bathroom renovation with premium fixtures and tiling',
    beforeImage: '/placeholder-bathroom-before.jpg',
    afterImage: '/placeholder-bathroom-after.jpg',
    featured: true
  },
  {
    id: 3,
    title: 'Loft Conversion',
    category: 'loft',
    location: 'Leicester',
    description: 'Spacious loft conversion creating additional bedroom space',
    beforeImage: '/placeholder-loft-before.jpg',
    afterImage: '/placeholder-loft-after.jpg',
    featured: false
  },
  {
    id: 4,
    title: 'External Wall Insulation',
    category: 'insulation',
    location: 'Nottingham',
    description: 'Energy-efficient external wall insulation with modern render finish',
    beforeImage: '/placeholder-insulation-before.jpg',
    afterImage: '/placeholder-insulation-after.jpg',
    featured: false
  },
  {
    id: 5,
    title: 'Complete Home Renovation',
    category: 'general',
    location: 'Derby',
    description: 'Full house renovation including flooring, decoration, and fixtures',
    beforeImage: '/placeholder-general-before.jpg',
    afterImage: '/placeholder-general-after.jpg',
    featured: true
  },
  {
    id: 6,
    title: 'Victorian Kitchen Restoration',
    category: 'kitchen',
    location: 'Leicester',
    description: 'Period kitchen restoration maintaining character while adding modern functionality',
    beforeImage: '/placeholder-kitchen2-before.jpg',
    afterImage: '/placeholder-kitchen2-after.jpg',
    featured: false
  }
]

const categories = [
  { id: 'all', name: 'All Projects', count: projects.length },
  { id: 'kitchen', name: 'Kitchens', count: projects.filter(p => p.category === 'kitchen').length },
  { id: 'bathroom', name: 'Bathrooms', count: projects.filter(p => p.category === 'bathroom').length },
  { id: 'loft', name: 'Lofts', count: projects.filter(p => p.category === 'loft').length },
  { id: 'insulation', name: 'Insulation', count: projects.filter(p => p.category === 'insulation').length },
  { id: 'general', name: 'General', count: projects.filter(p => p.category === 'general').length }
]

export default function OurWorkPage() {
  return (
    <div className="py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-heading font-bold text-kozak-charcoal mb-6">
            Our Work
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our portfolio of completed projects. Each renovation showcases our commitment 
            to quality craftsmanship and attention to detail. Click on any project to see the 
            before and after transformation.
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              className="px-6 py-3 rounded-lg font-semibold transition-colors duration-200 bg-gray-200 text-kozak-charcoal hover:bg-kozak-orange hover:text-white"
            >
              {category.name} ({category.count})
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {projects.map((project) => (
            <div key={project.id} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                {/* Placeholder for before/after slider */}
                <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center relative">
                  <div className="text-center text-gray-500">
                    <div className="text-3xl mb-2">🏠</div>
                    <div className="text-sm font-medium">Before & After</div>
                    <div className="text-xs">{project.title}</div>
                  </div>
                  
                  {/* Featured badge */}
                  {project.featured && (
                    <div className="absolute top-4 left-4 bg-kozak-orange text-white px-3 py-1 rounded-full text-sm font-semibold">
                      Featured
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-kozak-orange uppercase tracking-wide">
                      {project.category}
                    </span>
                    <span className="text-sm text-gray-500">{project.location}</span>
                  </div>
                  <h3 className="text-xl font-heading font-semibold text-kozak-charcoal mb-2 group-hover:text-kozak-orange transition-colors">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {project.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gray-50 rounded-lg p-12">
          <h2 className="text-3xl font-heading font-bold text-kozak-charcoal mb-4">
            Ready to Start Your Project?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Every project starts with a conversation. Contact us today to discuss your vision 
            and see how we can transform your space with the same quality and attention to detail.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" className="btn-primary">
              Get Free Quote
            </a>
            <a href="tel:07849768183" className="btn-secondary">
              Call: 07849768183
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
