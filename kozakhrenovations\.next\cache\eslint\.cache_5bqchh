[{"E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\contact\\page.tsx": "1", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\layout.tsx": "2", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\our-work\\page.tsx": "3", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\page.tsx": "4", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\bathroom-renovations\\page.tsx": "5", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\kitchen-fitting\\page.tsx": "6", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\page.tsx": "7", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\CTABanner.tsx": "8", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\Footer.tsx": "9", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\GalleryPreview.tsx": "10", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\Header.tsx": "11", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\Hero.tsx": "12", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\ServicesOverview.tsx": "13", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\TestimonialSnippet.tsx": "14", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\WhyChooseUs.tsx": "15", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\lib\\supabase.ts": "16", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\external-wall-insulation\\page.tsx": "17", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\general-renovations\\page.tsx": "18", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\loft-conversions\\page.tsx": "19", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\about\\page.tsx": "20", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\blog\\page.tsx": "21", "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\testimonials\\page.tsx": "22"}, {"size": 9634, "mtime": 1758051988643, "results": "23", "hashOfConfig": "24"}, {"size": 1014, "mtime": 1758051610318, "results": "25", "hashOfConfig": "24"}, {"size": 6920, "mtime": 1758051907559, "results": "26", "hashOfConfig": "24"}, {"size": 529, "mtime": 1758051691570, "results": "27", "hashOfConfig": "24"}, {"size": 7500, "mtime": 1758052315889, "results": "28", "hashOfConfig": "24"}, {"size": 8708, "mtime": 1758052324352, "results": "29", "hashOfConfig": "24"}, {"size": 5551, "mtime": 1758052034419, "results": "30", "hashOfConfig": "24"}, {"size": 1337, "mtime": 1758051768023, "results": "31", "hashOfConfig": "24"}, {"size": 3806, "mtime": 1758051649270, "results": "32", "hashOfConfig": "24"}, {"size": 3250, "mtime": 1758051746540, "results": "33", "hashOfConfig": "24"}, {"size": 5276, "mtime": 1758051633466, "results": "34", "hashOfConfig": "24"}, {"size": 1595, "mtime": 1758051707784, "results": "35", "hashOfConfig": "24"}, {"size": 2438, "mtime": 1758051720154, "results": "36", "hashOfConfig": "24"}, {"size": 1739, "mtime": 1758051971064, "results": "37", "hashOfConfig": "24"}, {"size": 1881, "mtime": 1758051730837, "results": "38", "hashOfConfig": "24"}, {"size": 898, "mtime": 1758051584923, "results": "39", "hashOfConfig": "24"}, {"size": 8447, "mtime": 1758052240584, "results": "40", "hashOfConfig": "24"}, {"size": 10289, "mtime": 1758052274725, "results": "41", "hashOfConfig": "24"}, {"size": 7678, "mtime": 1758052206813, "results": "42", "hashOfConfig": "24"}, {"size": 9982, "mtime": 1758052439357, "results": "43", "hashOfConfig": "24"}, {"size": 8748, "mtime": 1758052512806, "results": "44", "hashOfConfig": "24"}, {"size": 7843, "mtime": 1758052478044, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xnzto3", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\contact\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\layout.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\our-work\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\bathroom-renovations\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\kitchen-fitting\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\CTABanner.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\Footer.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\GalleryPreview.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\Header.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\Hero.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\ServicesOverview.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\TestimonialSnippet.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\components\\WhyChooseUs.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\lib\\supabase.ts", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\external-wall-insulation\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\general-renovations\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\services\\loft-conversions\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\about\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\blog\\page.tsx", [], [], "E:\\Vibe_Coding\\Kozak Website\\kozakhrenovations\\src\\app\\testimonials\\page.tsx", [], []]