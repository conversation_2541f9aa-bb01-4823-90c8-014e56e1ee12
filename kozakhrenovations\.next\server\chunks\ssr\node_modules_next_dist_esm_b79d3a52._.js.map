{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts", "turbopack:///[project]/node_modules/next/dist/esm/client/components/is-next-router-error.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/redirect-status-code.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/redirect-error.js", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "turbopack:///[project]/node_modules/next/dist/src/lib/framework/boundary-constants.tsx", "turbopack:///[project]/node_modules/next/dist/src/shared/lib/invariant-error.ts", "turbopack:///[project]/node_modules/next/dist/esm/server/app-render/dynamic-rendering.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/hooks-server-context.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/static-generation-bailout.js", "turbopack:///[project]/node_modules/next/dist/esm/server/dynamic-rendering-utils.js", "turbopack:///[project]/node_modules/next/dist/esm/lib/scheduler.js", "turbopack:///[project]/node_modules/next/dist/esm/server/lib/router-utils/is-postpone.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/unstable-rethrow.server.js", "turbopack:///[project]/node_modules/next/dist/src/client/components/bailout-to-client-rendering.ts", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/utils/warn-once.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/navigation-untracked.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/redirect-boundary.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/navigation.react-server.js", "turbopack:///[project]/node_modules/next/dist/esm/client/app-find-source-map-url.js", "turbopack:///[project]/node_modules/next/dist/esm/client/app-build-id.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/router-reducer/set-cache-busting-search-param.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/router-reducer/fetch-server-response.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/handle-isr-error.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/layout-router.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/error-boundary.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/disable-smooth-scroll.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/redirect.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/not-found.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/app-router-headers.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/match-segments.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/router-reducer/router-reducer-types.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/router-reducer/reducers/get-segment-value.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/router-reducer/create-href-from-url.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/segment.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/unrecognized-action-error.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/forbidden.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/unauthorized.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/is-bot.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/bfcache.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/cache-busting-search-param.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/segment-cache/segment-value-encoding.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/router-reducer/create-router-cache-key.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/navigation.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/use-action-queue.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/nav-failure-handler.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/interception-routes.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/unresolved-thenable.js", "turbopack:///[project]/node_modules/next/dist/esm/client/route-params.js", "turbopack:///[project]/node_modules/next/dist/esm/client/app-call-server.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/page-path/ensure-leading-slash.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/html-bots.js", "turbopack:///[project]/node_modules/next/dist/esm/client/flight-data-helpers.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/is-thenable.js", "turbopack:///[project]/node_modules/next/dist/esm/client/components/unstable-rethrow.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/hash.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/app-paths.js", "turbopack:///[project]/node_modules/next/dist/src/client/components/render-from-template-context.tsx", "turbopack:///[project]/node_modules/next/dist/esm/server/create-deduped-by-callsite-server-error-logger.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/utils/reflect-utils.js", "turbopack:///[project]/node_modules/next/dist/esm/server/web/spec-extension/adapters/reflect.js", "turbopack:///[project]/node_modules/next/dist/esm/server/request/utils.js", "turbopack:///[project]/node_modules/next/dist/esm/server/request/search-params.js", "turbopack:///[project]/node_modules/next/dist/src/server/request/params.ts", "turbopack:///[project]/node_modules/next/dist/src/client/components/client-page.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/client-segment.tsx", "turbopack:///[project]/node_modules/next/dist/src/lib/metadata/generate/icon-mark.tsx", "turbopack:///[project]/node_modules/next/dist/src/client/components/metadata/async-metadata.tsx", "turbopack:///[project]/node_modules/next/dist/src/lib/framework/boundary-components.tsx"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n", "import { isHTTPAccessFallbackError } from './http-access-fallback/http-access-fallback';\nimport { isRedirectError } from './redirect-error';\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */ export function isNextRouterError(error) {\n    return isRedirectError(error) || isHTTPAccessFallbackError(error);\n}\n\n//# sourceMappingURL=is-next-router-error.js.map", "export var RedirectStatusCode = /*#__PURE__*/ function(RedirectStatusCode) {\n    RedirectStatusCode[RedirectStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n    RedirectStatusCode[RedirectStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n    RedirectStatusCode[RedirectStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n    return RedirectStatusCode;\n}({});\n\n//# sourceMappingURL=redirect-status-code.js.map", "import { RedirectStatusCode } from './redirect-status-code';\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT';\nexport var RedirectType = /*#__PURE__*/ function(RedirectType) {\n    RedirectType[\"push\"] = \"push\";\n    RedirectType[\"replace\"] = \"replace\";\n    return RedirectType;\n}({});\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */ export function isRedirectError(error) {\n    if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n        return false;\n    }\n    const digest = error.digest.split(';');\n    const [errorCode, type] = digest;\n    const destination = digest.slice(2, -2).join(';');\n    const status = digest.at(-2);\n    const statusCode = Number(status);\n    return errorCode === REDIRECT_ERROR_CODE && (type === 'replace' || type === 'push') && typeof destination === 'string' && !isNaN(statusCode) && statusCode in RedirectStatusCode;\n}\n\n//# sourceMappingURL=redirect-error.js.map", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__'\n", "export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ // Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react';\nimport { DynamicServerError } from '../../client/components/hooks-server-context';\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout';\nimport { getRuntimeStagePromise, workUnitAsyncStorage } from './work-unit-async-storage.external';\nimport { workAsyncStorage } from '../app-render/work-async-storage.external';\nimport { makeHangingPromise } from '../dynamic-rendering-utils';\nimport { METADATA_BOUNDARY_NAME, VIEWPORT_BOUNDARY_NAME, OUTLET_BOUNDARY_NAME, ROOT_LAYOUT_BOUNDARY_NAME } from '../../lib/framework/boundary-constants';\nimport { scheduleOnNextTick } from '../../lib/scheduler';\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr';\nimport { InvariantError } from '../../shared/lib/invariant-error';\nconst hasPostpone = typeof React.unstable_postpone === 'function';\nexport function createDynamicTrackingState(isDebugDynamicAccesses) {\n    return {\n        isDebugDynamicAccesses,\n        dynamicAccesses: [],\n        syncDynamicErrorWithStack: null\n    };\n}\nexport function createDynamicValidationState() {\n    return {\n        hasSuspenseAboveBody: false,\n        hasDynamicMetadata: false,\n        hasDynamicViewport: false,\n        hasAllowedDynamic: false,\n        dynamicErrors: []\n    };\n}\nexport function getFirstDynamicReason(trackingState) {\n    var _trackingState_dynamicAccesses_;\n    return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */ export function markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'cache':\n            case 'unstable-cache':\n                // Inside cache scopes, marking a scope as dynamic has no effect,\n                // because the outer cache scope creates a cache boundary. This is\n                // subtly different from reading a dynamic data source, which is\n                // forbidden inside a cache scope.\n                return;\n            case 'private-cache':\n                // A private cache scope is already dynamic by definition.\n                return;\n            case 'prerender-legacy':\n            case 'prerender-ppr':\n            case 'request':\n                break;\n            default:\n                workUnitStore;\n        }\n    }\n    // If we're forcing dynamic rendering or we're forcing static rendering, we\n    // don't need to do anything here because the entire page is already dynamic\n    // or it's static and it should not throw or postpone here.\n    if (store.forceDynamic || store.forceStatic) return;\n    if (store.dynamicShouldError) {\n        throw Object.defineProperty(new StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n            value: \"E553\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender-ppr':\n                return postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n            case 'prerender-legacy':\n                workUnitStore.revalidate = 0;\n                // We aren't prerendering, but we are generating a static page. We need\n                // to bail out of static generation.\n                const err = Object.defineProperty(new DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E550\",\n                    enumerable: false,\n                    configurable: true\n                });\n                store.dynamicUsageDescription = expression;\n                store.dynamicUsageStack = err.stack;\n                throw err;\n            case 'request':\n                if (process.env.NODE_ENV !== 'production') {\n                    workUnitStore.usedDynamic = true;\n                }\n                break;\n            default:\n                workUnitStore;\n        }\n    }\n}\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */ export function throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n    // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n    const err = Object.defineProperty(new DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n        value: \"E558\",\n        enumerable: false,\n        configurable: true\n    });\n    prerenderStore.revalidate = 0;\n    store.dynamicUsageDescription = expression;\n    store.dynamicUsageStack = err.stack;\n    throw err;\n}\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */ export function trackDynamicDataInDynamicRender(workUnitStore) {\n    switch(workUnitStore.type){\n        case 'cache':\n        case 'unstable-cache':\n            // Inside cache scopes, marking a scope as dynamic has no effect,\n            // because the outer cache scope creates a cache boundary. This is\n            // subtly different from reading a dynamic data source, which is\n            // forbidden inside a cache scope.\n            return;\n        case 'private-cache':\n            // A private cache scope is already dynamic by definition.\n            return;\n        case 'prerender':\n        case 'prerender-runtime':\n        case 'prerender-legacy':\n        case 'prerender-ppr':\n        case 'prerender-client':\n            break;\n        case 'request':\n            if (process.env.NODE_ENV !== 'production') {\n                workUnitStore.usedDynamic = true;\n            }\n            break;\n        default:\n            workUnitStore;\n    }\n}\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n    const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`;\n    const error = createPrerenderInterruptedError(reason);\n    prerenderStore.controller.abort(error);\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nexport function abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        }\n    }\n}\nexport function trackSynchronousPlatformIOAccessInDev(requestStore) {\n    // We don't actually have a controller to abort but we do the semantic equivalent by\n    // advancing the request store out of prerender mode\n    requestStore.prerenderPhase = false;\n}\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */ export function abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n    const prerenderSignal = prerenderStore.controller.signal;\n    if (prerenderSignal.aborted === false) {\n        // TODO it would be better to move this aborted check into the callsite so we can avoid making\n        // the error object when it isn't relevant to the aborting of the prerender however\n        // since we need the throw semantics regardless of whether we abort it is easier to land\n        // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n        // to ideal implementation\n        abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n        // It is important that we set this tracking value after aborting. Aborts are executed\n        // synchronously except for the case where you abort during render itself. By setting this\n        // value late we can use it to determine if any of the aborted tasks are the task that\n        // called the sync IO expression in the first place.\n        const dynamicTracking = prerenderStore.dynamicTracking;\n        if (dynamicTracking) {\n            if (dynamicTracking.syncDynamicErrorWithStack === null) {\n                dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n            }\n        }\n    }\n    throw createPrerenderInterruptedError(`Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`);\n}\n/**\n * Use this function when dynamically prerendering with dynamicIO.\n * We don't want to error, because it's better to return something\n * (and we've already aborted the render at the point where the sync dynamic error occured),\n * but we should log an error server-side.\n * @internal\n */ export function warnOnSyncDynamicError(dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack) {\n        // the server did something sync dynamic, likely\n        // leading to an early termination of the prerender.\n        console.error(dynamicTracking.syncDynamicErrorWithStack);\n    }\n}\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nexport function Postpone({ reason, route }) {\n    const prerenderStore = workUnitAsyncStorage.getStore();\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n    postponeWithTracking(route, reason, dynamicTracking);\n}\nexport function postponeWithTracking(route, expression, dynamicTracking) {\n    assertPostpone();\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n    React.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n    return `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n}\nexport function isDynamicPostpone(err) {\n    if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n        return isDynamicPostponeReason(err.message);\n    }\n    return false;\n}\nfunction isDynamicPostponeReason(reason) {\n    return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n    throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n        value: \"E296\",\n        enumerable: false,\n        configurable: true\n    });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = NEXT_PRERENDER_INTERRUPTED;\n    return error;\n}\nexport function isPrerenderInterruptedError(error) {\n    return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nexport function accessedDynamicData(dynamicAccesses) {\n    return dynamicAccesses.length > 0;\n}\nexport function consumeDynamicAccess(serverDynamic, clientDynamic) {\n    // We mutate because we only call this once we are no longer writing\n    // to the dynamicTrackingState and it's more efficient than creating a new\n    // array.\n    serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n    return serverDynamic.dynamicAccesses;\n}\nexport function formatDynamicAPIAccesses(dynamicAccesses) {\n    return dynamicAccesses.filter((access)=>typeof access.stack === 'string' && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split('\\n')// Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes('node_modules/next/')) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(' (<anonymous>)')) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(' (node:')) {\n                return false;\n            }\n            return true;\n        }).join('\\n');\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E224\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */ export function createRenderInBrowserAbortSignal() {\n    const controller = new AbortController();\n    controller.abort(Object.defineProperty(new BailoutToCSRError('Render in Browser'), \"__NEXT_ERROR_CODE\", {\n        value: \"E721\",\n        enumerable: false,\n        configurable: true\n    }));\n    return controller.signal;\n}\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */ export function createHangingInputAbortSignal(workUnitStore) {\n    switch(workUnitStore.type){\n        case 'prerender':\n        case 'prerender-runtime':\n            const controller = new AbortController();\n            if (workUnitStore.cacheSignal) {\n                // If we have a cacheSignal it means we're in a prospective render. If\n                // the input we're waiting on is coming from another cache, we do want\n                // to wait for it so that we can resolve this cache entry too.\n                workUnitStore.cacheSignal.inputReady().then(()=>{\n                    controller.abort();\n                });\n            } else {\n                // Otherwise we're in the final render and we should already have all\n                // our caches filled.\n                // If the prerender uses stages, we have wait until the runtime stage,\n                // at which point all runtime inputs will be resolved.\n                // (otherwise, a runtime prerender might consider `cookies()` hanging\n                //  even though they'd resolve in the next task.)\n                //\n                // We might still be waiting on some microtasks so we\n                // wait one tick before giving up. When we give up, we still want to\n                // render the content of this cache as deeply as we can so that we can\n                // suspend as deeply as possible in the tree or not at all if we don't\n                // end up waiting for the input.\n                const runtimeStagePromise = getRuntimeStagePromise(workUnitStore);\n                if (runtimeStagePromise) {\n                    runtimeStagePromise.then(()=>scheduleOnNextTick(()=>controller.abort()));\n                } else {\n                    scheduleOnNextTick(()=>controller.abort());\n                }\n            }\n            return controller.signal;\n        case 'prerender-client':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n        case 'cache':\n        case 'private-cache':\n        case 'unstable-cache':\n            return undefined;\n        default:\n            workUnitStore;\n    }\n}\nexport function annotateDynamicAccess(expression, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nexport function useDynamicRouteParams(expression) {\n    const workStore = workAsyncStorage.getStore();\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workStore && workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender-client':\n            case 'prerender':\n                {\n                    const fallbackParams = workUnitStore.fallbackRouteParams;\n                    if (fallbackParams && fallbackParams.size > 0) {\n                        // We are in a prerender with cacheComponents semantics. We are going to\n                        // hang here and never resolve. This will cause the currently\n                        // rendering component to effectively be a dynamic hole.\n                        React.use(makeHangingPromise(workUnitStore.renderSignal, workStore.route, expression));\n                    }\n                    break;\n                }\n            case 'prerender-ppr':\n                {\n                    const fallbackParams = workUnitStore.fallbackRouteParams;\n                    if (fallbackParams && fallbackParams.size > 0) {\n                        return postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n                    }\n                    break;\n                }\n            case 'prerender-runtime':\n                throw Object.defineProperty(new InvariantError(`\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E771\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'cache':\n            case 'private-cache':\n                throw Object.defineProperty(new InvariantError(`\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E745\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'prerender-legacy':\n            case 'request':\n            case 'unstable-cache':\n                break;\n            default:\n                workUnitStore;\n        }\n    }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags = 'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6';\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(`\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`);\nconst hasMetadataRegex = new RegExp(`\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasViewportRegex = new RegExp(`\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`);\nexport function trackAllowedDynamicAccess(workStore, componentStack, dynamicValidation, clientDynamic) {\n    if (hasOutletRegex.test(componentStack)) {\n        // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n        return;\n    } else if (hasMetadataRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicMetadata = true;\n        return;\n    } else if (hasViewportRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicViewport = true;\n        return;\n    } else if (hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(componentStack)) {\n        // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n        // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n        // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n        dynamicValidation.hasAllowedDynamic = true;\n        dynamicValidation.hasSuspenseAboveBody = true;\n        return;\n    } else if (hasSuspenseRegex.test(componentStack)) {\n        // this error had a Suspense boundary above it so we don't need to report it as a source\n        // of disallowed\n        dynamicValidation.hasAllowedDynamic = true;\n        return;\n    } else if (clientDynamic.syncDynamicErrorWithStack) {\n        // This task was the task that called the sync error.\n        dynamicValidation.dynamicErrors.push(clientDynamic.syncDynamicErrorWithStack);\n        return;\n    } else {\n        const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;\n        const error = createErrorWithComponentOrOwnerStack(message, componentStack);\n        dynamicValidation.dynamicErrors.push(error);\n        return;\n    }\n}\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */ function createErrorWithComponentOrOwnerStack(message, componentStack) {\n    const ownerStack = process.env.NODE_ENV !== 'production' && React.captureOwnerStack ? React.captureOwnerStack() : null;\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.stack = error.name + ': ' + message + (ownerStack ?? componentStack);\n    return error;\n}\nexport var PreludeState = /*#__PURE__*/ function(PreludeState) {\n    PreludeState[PreludeState[\"Full\"] = 0] = \"Full\";\n    PreludeState[PreludeState[\"Empty\"] = 1] = \"Empty\";\n    PreludeState[PreludeState[\"Errored\"] = 2] = \"Errored\";\n    return PreludeState;\n}({});\nexport function logDisallowedDynamicError(workStore, error) {\n    console.error(error);\n    if (!workStore.dev) {\n        if (workStore.hasReadableErrorStacks) {\n            console.error(`To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`);\n        } else {\n            console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`);\n        }\n    }\n}\nexport function throwIfDisallowedDynamic(workStore, prelude, dynamicValidation, serverDynamic) {\n    if (prelude !== 0) {\n        if (dynamicValidation.hasSuspenseAboveBody) {\n            // This route has opted into allowing fully dynamic rendering\n            // by including a Suspense boundary above the body. In this case\n            // a lack of a shell is not considered disallowed so we simply return\n            return;\n        }\n        if (serverDynamic.syncDynamicErrorWithStack) {\n            // There is no shell and the server did something sync dynamic likely\n            // leading to an early termination of the prerender before the shell\n            // could be completed. We terminate the build/validating render.\n            logDisallowedDynamicError(workStore, serverDynamic.syncDynamicErrorWithStack);\n            throw new StaticGenBailoutError();\n        }\n        // We didn't have any sync bailouts but there may be user code which\n        // blocked the root. We would have captured these during the prerender\n        // and can log them here and then terminate the build/validating render\n        const dynamicErrors = dynamicValidation.dynamicErrors;\n        if (dynamicErrors.length > 0) {\n            for(let i = 0; i < dynamicErrors.length; i++){\n                logDisallowedDynamicError(workStore, dynamicErrors[i]);\n            }\n            throw new StaticGenBailoutError();\n        }\n        // If we got this far then the only other thing that could be blocking\n        // the root is dynamic Viewport. If this is dynamic then\n        // you need to opt into that by adding a Suspense boundary above the body\n        // to indicate your are ok with fully dynamic rendering.\n        if (dynamicValidation.hasDynamicViewport) {\n            console.error(`Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`);\n            throw new StaticGenBailoutError();\n        }\n        if (prelude === 1) {\n            // If we ever get this far then we messed up the tracking of invalid dynamic.\n            // We still adhere to the constraint that you must produce a shell but invite the\n            // user to report this as a bug in Next.js.\n            console.error(`Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`);\n            throw new StaticGenBailoutError();\n        }\n    } else {\n        if (dynamicValidation.hasAllowedDynamic === false && dynamicValidation.hasDynamicMetadata) {\n            console.error(`Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`);\n            throw new StaticGenBailoutError();\n        }\n    }\n}\nexport function delayUntilRuntimeStage(prerenderStore, result) {\n    if (prerenderStore.runtimeStagePromise) {\n        return prerenderStore.runtimeStagePromise.then(()=>result);\n    }\n    return result;\n}\n\n//# sourceMappingURL=dynamic-rendering.js.map", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE';\nexport class DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description), this.description = description, this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nexport function isDynamicServerError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err) || typeof err.digest !== 'string') {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\n\n//# sourceMappingURL=hooks-server-context.js.map", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT';\nexport class StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args), this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nexport function isStaticGenBailoutError(error) {\n    if (typeof error !== 'object' || error === null || !('code' in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\n\n//# sourceMappingURL=static-generation-bailout.js.map", "export function isHangingPromiseRejectionError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err)) {\n        return false;\n    }\n    return err.digest === HANGING_PROMISE_REJECTION;\n}\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION';\nclass HangingPromiseRejectionError extends Error {\n    constructor(route, expression){\n        super(`During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route \"${route}\".`), this.route = route, this.expression = expression, this.digest = HANGING_PROMISE_REJECTION;\n    }\n}\nconst abortListenersBySignal = new WeakMap();\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for cacheComponents where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */ export function makeHangingPromise(signal, route, expression) {\n    if (signal.aborted) {\n        return Promise.reject(new HangingPromiseRejectionError(route, expression));\n    } else {\n        const hangingPromise = new Promise((_, reject)=>{\n            const boundRejection = reject.bind(null, new HangingPromiseRejectionError(route, expression));\n            let currentListeners = abortListenersBySignal.get(signal);\n            if (currentListeners) {\n                currentListeners.push(boundRejection);\n            } else {\n                const listeners = [\n                    boundRejection\n                ];\n                abortListenersBySignal.set(signal, listeners);\n                signal.addEventListener('abort', ()=>{\n                    for(let i = 0; i < listeners.length; i++){\n                        listeners[i]();\n                    }\n                }, {\n                    once: true\n                });\n            }\n        });\n        // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n        // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n        // your own promise out of it you'll need to ensure you handle the error when it rejects.\n        hangingPromise.catch(ignoreReject);\n        return hangingPromise;\n    }\n}\nfunction ignoreReject() {}\nexport function makeDevtoolsIOAwarePromise(underlying) {\n    // in React DevTools if we resolve in a setTimeout we will observe\n    // the promise resolution as something that can suspend a boundary or root.\n    return new Promise((resolve)=>{\n        // Must use setTimeout to be considered IO React DevTools. setImmediate will not work.\n        setTimeout(()=>{\n            resolve(underlying);\n        }, 0);\n    });\n}\n\n//# sourceMappingURL=dynamic-rendering-utils.js.map", "/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */ export const scheduleOnNextTick = (cb)=>{\n    // We use Promise.resolve().then() here so that the operation is scheduled at\n    // the end of the promise job queue, we then add it to the next process tick\n    // to ensure it's evaluated afterwards.\n    //\n    // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n    //\n    Promise.resolve().then(()=>{\n        if (process.env.NEXT_RUNTIME === 'edge') {\n            setTimeout(cb, 0);\n        } else {\n            process.nextTick(cb);\n        }\n    });\n};\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */ export const scheduleImmediate = (cb)=>{\n    if (process.env.NEXT_RUNTIME === 'edge') {\n        setTimeout(cb, 0);\n    } else {\n        setImmediate(cb);\n    }\n};\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */ export function atLeastOneTask() {\n    return new Promise((resolve)=>scheduleImmediate(resolve));\n}\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */ export function waitAtLeastOneReactRenderTask() {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n        return new Promise((r)=>setTimeout(r, 0));\n    } else {\n        return new Promise((r)=>setImmediate(r));\n    }\n}\n\n//# sourceMappingURL=scheduler.js.map", "const REACT_POSTPONE_TYPE = Symbol.for('react.postpone');\nexport function isPostpone(error) {\n    return typeof error === 'object' && error !== null && error.$$typeof === REACT_POSTPONE_TYPE;\n}\n\n//# sourceMappingURL=is-postpone.js.map", "import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils';\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone';\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr';\nimport { isNextRouterError } from './is-next-router-error';\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering';\nimport { isDynamicServerError } from './hooks-server-context';\nexport function unstable_rethrow(error) {\n    if (isNextRouterError(error) || isBailoutToCSRError(error) || isDynamicServerError(error) || isDynamicPostpone(error) || isPostpone(error) || isHangingPromiseRejectionError(error)) {\n        throw error;\n    }\n    if (error instanceof Error && 'cause' in error) {\n        unstable_rethrow(error.cause);\n    }\n}\n\n//# sourceMappingURL=unstable-rethrow.server.js.map", "import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../server/app-render/work-unit-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-runtime':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        throw new BailoutToCSRError(reason)\n      case 'request':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n", "let warnOnce = (_)=>{};\nif (process.env.NODE_ENV !== 'production') {\n    const warnings = new Set();\n    warnOnce = (msg)=>{\n        if (!warnings.has(msg)) {\n            console.warn(msg);\n        }\n        warnings.add(msg);\n    };\n}\nexport { warnOnce };\n\n//# sourceMappingURL=warn-once.js.map", "'use client';\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */ import React, { useContext } from 'react';\nimport { useUntrackedPathname } from '../navigation-untracked';\nimport { HTTPAccessErrorStatus, getAccessFallbackHTTPStatus, getAccessFallbackErrorTypeByStatus, isHTTPAccessFallbackError } from './http-access-fallback';\nimport { warnOnce } from '../../../shared/lib/utils/warn-once';\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime';\nclass HTTPAccessFallbackErrorBoundary extends React.Component {\n    componentDidCatch() {\n        if (process.env.NODE_ENV === 'development' && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            warnOnce(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if (isHTTPAccessFallbackError(error)) {\n            const httpStatus = getAccessFallbackHTTPStatus(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ _jsxs(_Fragment, {\n                children: [\n                    /*#__PURE__*/ _jsx(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                    process.env.NODE_ENV === 'development' && /*#__PURE__*/ _jsx(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: getAccessFallbackErrorTypeByStatus(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nexport function HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = useUntrackedPathname();\n    const missingSlots = useContext(MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ _jsx(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ _jsx(_Fragment, {\n        children: children\n    });\n}\n\n//# sourceMappingURL=error-boundary.js.map", "import { useContext } from 'react';\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime';\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */ function hasFallbackRouteParams() {\n    if (typeof window === 'undefined') {\n        // AsyncLocalStorage should not be included in the client bundle.\n        const { workUnitAsyncStorage } = require('../../server/app-render/work-unit-async-storage.external');\n        const workUnitStore = workUnitAsyncStorage.getStore();\n        if (!workUnitStore) return false;\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-client':\n            case 'prerender-ppr':\n                const fallbackParams = workUnitStore.fallbackRouteParams;\n                return fallbackParams ? fallbackParams.size > 0 : false;\n            case 'prerender-legacy':\n            case 'request':\n            case 'prerender-runtime':\n            case 'cache':\n            case 'private-cache':\n            case 'unstable-cache':\n                break;\n            default:\n                workUnitStore;\n        }\n        return false;\n    }\n    return false;\n}\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */ export function useUntrackedPathname() {\n    // If there are any unknown route parameters we would typically throw\n    // an error, but this internal method allows us to return a null value instead\n    // for components that do not propagate the pathname to the static shell (like\n    // the error boundary).\n    if (hasFallbackRouteParams()) {\n        return null;\n    }\n    // This shouldn't cause any issues related to conditional rendering because\n    // the environment will be consistent for the render.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useContext(PathnameContext);\n}\n\n//# sourceMappingURL=navigation-untracked.js.map", "'use client';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { useEffect } from 'react';\nimport { useRouter } from './navigation';\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect';\nimport { RedirectType, isRedirectError } from './redirect-error';\nfunction HandleRedirect(param) {\n    let { redirect, reset, redirectType } = param;\n    const router = useRouter();\n    useEffect(()=>{\n        React.startTransition(()=>{\n            if (redirectType === RedirectType.push) {\n                router.push(redirect, {});\n            } else {\n                router.replace(redirect, {});\n            }\n            reset();\n        });\n    }, [\n        redirect,\n        redirectType,\n        reset,\n        router\n    ]);\n    return null;\n}\nexport class RedirectErrorBoundary extends React.Component {\n    static getDerivedStateFromError(error) {\n        if (isRedirectError(error)) {\n            const url = getURLFromRedirectError(error);\n            const redirectType = getRedirectTypeFromError(error);\n            return {\n                redirect: url,\n                redirectType\n            };\n        }\n        // Re-throw if error is not for redirect\n        throw error;\n    }\n    // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n    render() {\n        const { redirect, redirectType } = this.state;\n        if (redirect !== null && redirectType !== null) {\n            return /*#__PURE__*/ _jsx(HandleRedirect, {\n                redirect: redirect,\n                redirectType: redirectType,\n                reset: ()=>this.setState({\n                        redirect: null\n                    })\n            });\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            redirect: null,\n            redirectType: null\n        };\n    }\n}\nexport function RedirectBoundary(param) {\n    let { children } = param;\n    const router = useRouter();\n    return /*#__PURE__*/ _jsx(RedirectErrorBoundary, {\n        router: router,\n        children: children\n    });\n}\n\n//# sourceMappingURL=redirect-boundary.js.map", "/** @internal */ class ReadonlyURLSearchParamsError extends Error {\n    constructor(){\n        super('Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams');\n    }\n}\nclass ReadonlyURLSearchParams extends URLSearchParams {\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ append() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ delete() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ set() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n    /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */ sort() {\n        throw new ReadonlyURLSearchParamsError();\n    }\n}\nexport function unstable_isUnrecognizedActionError() {\n    throw Object.defineProperty(new Error('`unstable_isUnrecognizedActionError` can only be used on the client.'), \"__NEXT_ERROR_CODE\", {\n        value: \"E776\",\n        enumerable: false,\n        configurable: true\n    });\n}\nexport { redirect, permanentRedirect } from './redirect';\nexport { RedirectType } from './redirect-error';\nexport { notFound } from './not-found';\nexport { forbidden } from './forbidden';\nexport { unauthorized } from './unauthorized';\nexport { unstable_rethrow } from './unstable-rethrow';\nexport { ReadonlyURLSearchParams };\n\n//# sourceMappingURL=navigation.react-server.js.map", "const basePath = process.env.__NEXT_ROUTER_BASEPATH || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nexport const findSourceMapURL = process.env.NODE_ENV === 'development' ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : undefined;\n\n//# sourceMappingURL=app-find-source-map-url.js.map", "// This gets assigned as a side-effect during app initialization. Because it\n// represents the build used to create the JS bundle, it should never change\n// after being set, so we store it in a global variable.\n//\n// When performing RSC requests, if the incoming data has a different build ID,\n// we perform an MPA navigation/refresh to load the updated build and ensure\n// that the client and server in sync.\n// Starts as an empty string. In practice, because setAppBuildId is called\n// during initialization before hydration starts, this will always get\n// reassigned to the actual build ID before it's ever needed by a navigation.\n// If for some reasons it didn't, due to a bug or race condition, then on\n// navigation the build comparision would fail and trigger an MPA navigation.\nlet globalBuildId = '';\nexport function setAppBuildId(buildId) {\n    globalBuildId = buildId;\n}\nexport function getAppBuildId() {\n    return globalBuildId;\n}\n\n//# sourceMappingURL=app-build-id.js.map", "'use client';\nimport { computeCacheBustingSearchParam } from '../../../shared/lib/router/utils/cache-busting-search-param';\nimport { NEXT_ROUTER_PREFETCH_HEADER, NEXT_ROUTER_SEGMENT_PREFETCH_HEADER, NEXT_ROUTER_STATE_TREE_HEADER, NEXT_URL, NEXT_RSC_UNION_QUERY } from '../app-router-headers';\n/**\n * Mutates the provided URL by adding a cache-busting search parameter for CDNs that don't\n * support custom headers. This helps avoid caching conflicts by making each request unique.\n *\n * Rather than relying on the Vary header which some CDNs ignore, we append a search param\n * to create a unique URL that forces a fresh request.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * Note: This function mutates the input URL directly and does not return anything.\n *\n * TODO: Since we need to use a search param anyway, we could simplify by removing the custom\n * headers approach entirely and just use search params.\n */ export const setCacheBustingSearchParam = (url, headers)=>{\n    const uniqueCacheKey = computeCacheBustingSearchParam(headers[NEXT_ROUTER_PREFETCH_HEADER], headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER], headers[NEXT_ROUTER_STATE_TREE_HEADER], headers[NEXT_URL]);\n    setCacheBustingSearchParamWithHash(url, uniqueCacheKey);\n};\n/**\n * Sets a cache-busting search parameter on a URL using a provided hash value.\n *\n * This function performs the same logic as `setCacheBustingSearchParam` but accepts\n * a pre-computed hash instead of computing it from headers.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * hash: \"abc123\"\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * If the hash is null, we will set `_rsc` search param without a value.\n * Like this: https://example.com/path?query=1&_rsc\n *\n * Note: This function mutates the input URL directly and does not return anything.\n */ export const setCacheBustingSearchParamWithHash = (url, hash)=>{\n    /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */ const existingSearch = url.search;\n    const rawQuery = existingSearch.startsWith('?') ? existingSearch.slice(1) : existingSearch;\n    // Always remove any existing cache busting param and add a fresh one to ensure\n    // we have the correct value based on current request headers\n    const pairs = rawQuery.split('&').filter((pair)=>pair && !pair.startsWith(\"\" + NEXT_RSC_UNION_QUERY + \"=\"));\n    if (hash.length > 0) {\n        pairs.push(NEXT_RSC_UNION_QUERY + \"=\" + hash);\n    } else {\n        pairs.push(\"\" + NEXT_RSC_UNION_QUERY);\n    }\n    url.search = pairs.length ? \"?\" + pairs.join('&') : '';\n};\n\n//# sourceMappingURL=set-cache-busting-search-param.js.map", "'use client';\n// TODO: Explicitly import from client.browser\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { createFromReadableStream as createFromReadableStreamBrowser } from 'react-server-dom-webpack/client';\nimport { NEXT_ROUTER_PREFETCH_HEADER, NEXT_ROUTER_STATE_TREE_HEADER, NEXT_RSC_UNION_QUERY, NEXT_URL, RSC_HEADER, RSC_CONTENT_TYPE_HEADER, NEXT_HMR_REFRESH_HEADER, NEXT_DID_POSTPONE_HEADER, NEXT_ROUTER_STALE_TIME_HEADER } from '../app-router-headers';\nimport { callServer } from '../../app-call-server';\nimport { findSourceMapURL } from '../../app-find-source-map-url';\nimport { PrefetchKind } from './router-reducer-types';\nimport { normalizeFlightData, prepareFlightRouterStateForRequest } from '../../flight-data-helpers';\nimport { getAppBuildId } from '../../app-build-id';\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param';\nimport { urlToUrlWithoutFlightMarker } from '../../route-params';\nconst createFromReadableStream = createFromReadableStreamBrowser;\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(new URL(url, location.origin)).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (typeof window !== 'undefined') {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */ export async function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [RSC_HEADER]: '1',\n        // Provide the current router state\n        [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(flightRouterState, options.isHmrRefresh)\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === PrefetchKind.AUTO) {\n        headers[NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n        headers[NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (process.env.NODE_ENV === 'production') {\n            if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n                // In \"output: export\" mode, we can't rely on headers to distinguish\n                // between HTML and RSC requests. Instead, we append an extra prefix\n                // to the request.\n                url = new URL(url);\n                if (url.pathname.endsWith('/')) {\n                    url.pathname += 'index.txt';\n                } else {\n                    url.pathname += '.txt';\n                }\n            }\n        }\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(new URL(res.url));\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(NEXT_URL));\n        const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeaderSeconds = res.headers.get(NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeaderSeconds !== null ? parseInt(staleTimeHeaderSeconds, 10) * 1000 : -1;\n        let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER);\n        if (process.env.NODE_ENV === 'production') {\n            if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n                if (!isFlightResponse) {\n                    isFlightResponse = contentType.startsWith('text/plain');\n                }\n            }\n        }\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n            await require('../../dev/hot-reloader/app/hot-reloader-app').waitForWebpackRuntimeHotUpdate();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if (getAppBuildId() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: normalizeFlightData(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nexport async function createFetch(url, headers, fetchPriority, signal) {\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n        headers['Next-Test-Fetch-Priority'] = fetchPriority;\n    }\n    if (process.env.NEXT_DEPLOYMENT_ID) {\n        headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID;\n    }\n    const fetchOptions = {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    };\n    // `fetchUrl` is slightly different from `url` because we add a cache-busting\n    // search param to it. This should not leak outside of this function, so we\n    // track them separately.\n    let fetchUrl = new URL(url);\n    setCacheBustingSearchParam(fetchUrl, headers);\n    let browserResponse = await fetch(fetchUrl, fetchOptions);\n    // If the server responds with a redirect (e.g. 307), and the redirected\n    // location does not contain the cache busting search param set in the\n    // original request, the response is likely invalid — when following the\n    // redirect, the browser forwards the request headers, but since the cache\n    // busting search param is missing, the server will reject the request due to\n    // a mismatch.\n    //\n    // Ideally, we would be able to intercept the redirect response and perform it\n    // manually, instead of letting the browser automatically follow it, but this\n    // is not allowed by the fetch API.\n    //\n    // So instead, we must \"replay\" the redirect by fetching the new location\n    // again, but this time we'll append the cache busting search param to prevent\n    // a mismatch.\n    //\n    // TODO: We can optimize Next.js's built-in middleware APIs by returning a\n    // custom status code, to prevent the browser from automatically following it.\n    //\n    // This does not affect Server Action-based redirects; those are encoded\n    // differently, as part of the Flight body. It only affects redirects that\n    // occur in a middleware or a third-party proxy.\n    let redirected = browserResponse.redirected;\n    if (process.env.__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS) {\n        // This is to prevent a redirect loop. Same limit used by Chrome.\n        const MAX_REDIRECTS = 20;\n        for(let n = 0; n < MAX_REDIRECTS; n++){\n            if (!browserResponse.redirected) {\n                break;\n            }\n            const responseUrl = new URL(browserResponse.url, fetchUrl);\n            if (responseUrl.origin !== fetchUrl.origin) {\n                break;\n            }\n            if (responseUrl.searchParams.get(NEXT_RSC_UNION_QUERY) === fetchUrl.searchParams.get(NEXT_RSC_UNION_QUERY)) {\n                break;\n            }\n            // The RSC request was redirected. Assume the response is invalid.\n            //\n            // Append the cache busting search param to the redirected URL and\n            // fetch again.\n            fetchUrl = new URL(responseUrl);\n            setCacheBustingSearchParam(fetchUrl, headers);\n            browserResponse = await fetch(fetchUrl, fetchOptions);\n            // We just performed a manual redirect, so this is now true.\n            redirected = true;\n        }\n    }\n    // Remove the cache busting search param from the response URL, to prevent it\n    // from leaking outside of this function.\n    const responseUrl = new URL(browserResponse.url, fetchUrl);\n    responseUrl.searchParams.delete(NEXT_RSC_UNION_QUERY);\n    const rscResponse = {\n        url: responseUrl.href,\n        // This is true if any redirects occurred, either automatically by the\n        // browser, or manually by us. So it's different from\n        // `browserResponse.redirected`, which only tells us whether the browser\n        // followed a redirect, and only for the last response in the chain.\n        redirected,\n        // These can be copied from the last browser response we received. We\n        // intentionally only expose the subset of fields that are actually used\n        // elsewhere in the codebase.\n        ok: browserResponse.ok,\n        headers: browserResponse.headers,\n        body: browserResponse.body,\n        status: browserResponse.status\n    };\n    return rscResponse;\n}\nexport function createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer,\n        findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\n\n//# sourceMappingURL=fetch-server-response.js.map", "const workAsyncStorage = typeof window === 'undefined' ? require('../../server/app-render/work-async-storage.external').workAsyncStorage : undefined;\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError(param) {\n    let { error } = param;\n    if (workAsyncStorage) {\n        const store = workAsyncStorage.getStore();\n        if ((store == null ? void 0 : store.isRevalidate) || (store == null ? void 0 : store.isStaticGeneration)) {\n            console.error(error);\n            throw error;\n        }\n    }\n    return null;\n}\n\n//# sourceMappingURL=handle-isr-error.js.map", "'use client';\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { ACTION_SERVER_PATCH } from './router-reducer/router-reducer-types';\nimport React, { useContext, use, startTransition, Suspense, useDeferredValue } from 'react';\nimport ReactDOM from 'react-dom';\nimport { LayoutRouterContext, GlobalLayoutRouterContext, TemplateContext } from '../../shared/lib/app-router-context.shared-runtime';\nimport { fetchServerResponse } from './router-reducer/fetch-server-response';\nimport { unresolvedThenable } from './unresolved-thenable';\nimport { ErrorBoundary } from './error-boundary';\nimport { matchSegment } from './match-segments';\nimport { disableSmoothScrollDuringRouteTransition } from '../../shared/lib/router/utils/disable-smooth-scroll';\nimport { RedirectBoundary } from './redirect-boundary';\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary';\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key';\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree';\nimport { dispatchAppRouterAction } from './use-action-queue';\nimport { useRouterBFCache } from './bfcache';\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths';\nconst Activity = process.env.__NEXT_ROUTER_BF_CACHE ? require('react').unstable_Activity : null;\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if (matchSegment(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                'refetch'\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = ReactDOM.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (typeof window === 'undefined') return null;\n    // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n    // We need to lazily reference it.\n    const internal_reactDOMfindDOMNode = __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode;\n    return internal_reactDOMfindDOMNode(instance);\n}\nconst rectProperties = [\n    'bottom',\n    'height',\n    'left',\n    'right',\n    'top',\n    'width',\n    'x',\n    'y'\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        'sticky',\n        'fixed'\n    ].includes(getComputedStyle(element).position)) {\n        if (process.env.NODE_ENV === 'development') {\n            console.warn('Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:', element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === 'top') {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends React.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args), this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>matchSegment(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    if (process.env.NODE_ENV !== 'production') {\n                        var _domNode_parentElement;\n                        if (((_domNode_parentElement = domNode.parentElement) == null ? void 0 : _domNode_parentElement.localName) === 'head') {\n                        // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n                        // This is always a bug in Next.js and caused by React hoisting metadata.\n                        // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n                        }\n                    }\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                disableSmoothScrollDuringRouteTransition(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        ;\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        // Scroll into view doesn't scroll horizontally by default when not needed\n                        ;\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `disableSmoothScrollDuringRouteTransition`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = useContext(GlobalLayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant global layout router not mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E473\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return /*#__PURE__*/ _jsx(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef,\n        children: children\n    });\n}\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { tree, segmentPath, cacheNode, url } = param;\n    const context = useContext(GlobalLayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant global layout router not mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E473\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const { tree: fullTree } = context;\n    // `rsc` represents the renderable node for this segment.\n    // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n    // We should use that on initial render instead of `rsc`. Then we'll switch\n    // to `rsc` when the dynamic response streams in.\n    //\n    // If no prefetch data is available, then we go straight to rendering `rsc`.\n    const resolvedPrefetchRsc = cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    const rsc = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc);\n    // `rsc` is either a React node or a promise for a React node, except we\n    // special case `null` to represent that this segment's data is missing. If\n    // it's a promise, we need to unwrap it so we can determine whether or not the\n    // data is missing.\n    const resolvedRsc = typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function' ? use(rsc) : rsc;\n    if (!resolvedRsc) {\n        // The data for this segment is not available, and there's no pending\n        // navigation that will be able to fulfill it. We need to fetch more from\n        // the server and patch the cache.\n        // Check if there's already a pending request.\n        let lazyData = cacheNode.lazyData;\n        if (lazyData === null) {\n            /**\n       * Router state with refetch marker added\n       */ // TODO-APP: remove ''\n            const refetchTree = walkAddRefetch([\n                '',\n                ...segmentPath\n            ], fullTree);\n            const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree);\n            const navigatedAt = Date.now();\n            cacheNode.lazyData = lazyData = fetchServerResponse(new URL(url, location.origin), {\n                flightRouterState: refetchTree,\n                nextUrl: includeNextUrl ? context.nextUrl : null\n            }).then((serverResponse)=>{\n                startTransition(()=>{\n                    dispatchAppRouterAction({\n                        type: ACTION_SERVER_PATCH,\n                        previousTree: fullTree,\n                        serverResponse,\n                        navigatedAt\n                    });\n                });\n                return serverResponse;\n            });\n            // Suspend while waiting for lazyData to resolve\n            use(lazyData);\n        }\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n        use(unresolvedThenable);\n    }\n    // If we get to this point, then we know we have something we can render.\n    const subtree = // The layout router context narrows down tree and childNodes at each level.\n    /*#__PURE__*/ _jsx(LayoutRouterContext.Provider, {\n        value: {\n            parentTree: tree,\n            parentCacheNode: cacheNode,\n            parentSegmentPath: segmentPath,\n            // TODO-APP: overriding of url for parallel routes\n            url: url\n        },\n        children: resolvedRsc\n    });\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { loading, children } = param;\n    // If loading is a promise, unwrap it. This happens in cases where we haven't\n    // yet received the loading data from the server — which includes whether or\n    // not this layout has a loading component at all.\n    //\n    // It's OK to suspend here instead of inside the fallback because this\n    // promise will resolve simultaneously with the data for the segment itself.\n    // So it will never suspend for longer than it would have if we didn't use\n    // a Suspense fallback at all.\n    let loadingModuleData;\n    if (typeof loading === 'object' && loading !== null && typeof loading.then === 'function') {\n        const promiseForLoading = loading;\n        loadingModuleData = use(promiseForLoading);\n    } else {\n        loadingModuleData = loading;\n    }\n    if (loadingModuleData) {\n        const loadingRsc = loadingModuleData[0];\n        const loadingStyles = loadingModuleData[1];\n        const loadingScripts = loadingModuleData[2];\n        return /*#__PURE__*/ _jsx(Suspense, {\n            fallback: /*#__PURE__*/ _jsxs(_Fragment, {\n                children: [\n                    loadingStyles,\n                    loadingScripts,\n                    loadingRsc\n                ]\n            }),\n            children: children\n        });\n    }\n    return /*#__PURE__*/ _jsx(_Fragment, {\n        children: children\n    });\n}\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */ export default function OuterLayoutRouter(param) {\n    let { parallelRouterKey, error, errorStyles, errorScripts, templateStyles, templateScripts, template, notFound, forbidden, unauthorized, segmentViewBoundaries } = param;\n    const context = useContext(LayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant expected layout router to be mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E56\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const { parentTree, parentCacheNode, parentSegmentPath, url } = context;\n    // Get the CacheNode for this segment by reading it from the parent segment's\n    // child map.\n    const parentParallelRoutes = parentCacheNode.parallelRoutes;\n    let segmentMap = parentParallelRoutes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!segmentMap) {\n        segmentMap = new Map();\n        parentParallelRoutes.set(parallelRouterKey, segmentMap);\n    }\n    const parentTreeSegment = parentTree[0];\n    const segmentPath = parentSegmentPath === null ? // path. This has led to a bunch of special cases scattered throughout\n    // the code. We should clean this up.\n    [\n        parallelRouterKey\n    ] : parentSegmentPath.concat([\n        parentTreeSegment,\n        parallelRouterKey\n    ]);\n    // The \"state\" key of a segment is the one passed to React — it represents the\n    // identity of the UI tree. Whenever the state key changes, the tree is\n    // recreated and the state is reset. In the App Router model, search params do\n    // not cause state to be lost, so two segments with the same segment path but\n    // different search params should have the same state key.\n    //\n    // The \"cache\" key of a segment, however, *does* include the search params, if\n    // it's possible that the segment accessed the search params on the server.\n    // (This only applies to page segments; layout segments cannot access search\n    // params on the server.)\n    const activeTree = parentTree[1][parallelRouterKey];\n    const activeSegment = activeTree[0];\n    const activeStateKey = createRouterCacheKey(activeSegment, true) // no search params\n    ;\n    // At each level of the route tree, not only do we render the currently\n    // active segment — we also render the last N segments that were active at\n    // this level inside a hidden <Activity> boundary, to preserve their state\n    // if or when the user navigates to them again.\n    //\n    // bfcacheEntry is a linked list of FlightRouterStates.\n    let bfcacheEntry = useRouterBFCache(activeTree, activeStateKey);\n    let children = [];\n    do {\n        const tree = bfcacheEntry.tree;\n        const stateKey = bfcacheEntry.stateKey;\n        const segment = tree[0];\n        const cacheKey = createRouterCacheKey(segment);\n        // Read segment path from the parallel router cache node.\n        let cacheNode = segmentMap.get(cacheKey);\n        if (cacheNode === undefined) {\n            // When data is not available during rendering client-side we need to fetch\n            // it from the server.\n            const newLazyCacheNode = {\n                lazyData: null,\n                rsc: null,\n                prefetchRsc: null,\n                head: null,\n                prefetchHead: null,\n                parallelRoutes: new Map(),\n                loading: null,\n                navigatedAt: -1\n            };\n            // Flight data fetch kicked off during render and put into the cache.\n            cacheNode = newLazyCacheNode;\n            segmentMap.set(cacheKey, newLazyCacheNode);\n        }\n        /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n      - When gracefully degrade for bots, skip rendering error boundary.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */ let segmentBoundaryTriggerNode = null;\n        let segmentViewStateNode = null;\n        if (process.env.NODE_ENV !== 'production' && process.env.__NEXT_DEVTOOL_SEGMENT_EXPLORER) {\n            const { SegmentBoundaryTriggerNode, SegmentViewStateNode } = require('../../next-devtools/userspace/app/segment-explorer-node');\n            const pagePrefix = normalizeAppPath(url);\n            segmentViewStateNode = /*#__PURE__*/ _jsx(SegmentViewStateNode, {\n                page: pagePrefix\n            }, pagePrefix);\n            segmentBoundaryTriggerNode = /*#__PURE__*/ _jsx(_Fragment, {\n                children: /*#__PURE__*/ _jsx(SegmentBoundaryTriggerNode, {})\n            });\n        }\n        // TODO: The loading module data for a segment is stored on the parent, then\n        // applied to each of that parent segment's parallel route slots. In the\n        // simple case where there's only one parallel route (the `children` slot),\n        // this is no different from if the loading module data where stored on the\n        // child directly. But I'm not sure this actually makes sense when there are\n        // multiple parallel routes. It's not a huge issue because you always have\n        // the option to define a narrower loading boundary for a particular slot. But\n        // this sort of smells like an implementation accident to me.\n        const loadingModuleData = parentCacheNode.loading;\n        let child = /*#__PURE__*/ _jsxs(TemplateContext.Provider, {\n            value: /*#__PURE__*/ _jsxs(ScrollAndFocusHandler, {\n                segmentPath: segmentPath,\n                children: [\n                    /*#__PURE__*/ _jsx(ErrorBoundary, {\n                        errorComponent: error,\n                        errorStyles: errorStyles,\n                        errorScripts: errorScripts,\n                        children: /*#__PURE__*/ _jsx(LoadingBoundary, {\n                            loading: loadingModuleData,\n                            children: /*#__PURE__*/ _jsx(HTTPAccessFallbackBoundary, {\n                                notFound: notFound,\n                                forbidden: forbidden,\n                                unauthorized: unauthorized,\n                                children: /*#__PURE__*/ _jsxs(RedirectBoundary, {\n                                    children: [\n                                        /*#__PURE__*/ _jsx(InnerLayoutRouter, {\n                                            url: url,\n                                            tree: tree,\n                                            cacheNode: cacheNode,\n                                            segmentPath: segmentPath\n                                        }),\n                                        segmentBoundaryTriggerNode\n                                    ]\n                                })\n                            })\n                        })\n                    }),\n                    segmentViewStateNode\n                ]\n            }),\n            children: [\n                templateStyles,\n                templateScripts,\n                template\n            ]\n        }, stateKey);\n        if (process.env.NODE_ENV !== 'production') {\n            const { SegmentStateProvider } = require('../../next-devtools/userspace/app/segment-explorer-node');\n            child = /*#__PURE__*/ _jsxs(SegmentStateProvider, {\n                children: [\n                    child,\n                    segmentViewBoundaries\n                ]\n            }, stateKey);\n        }\n        if (process.env.__NEXT_ROUTER_BF_CACHE) {\n            child = /*#__PURE__*/ _jsx(Activity, {\n                mode: stateKey === activeStateKey ? 'visible' : 'hidden',\n                children: child\n            }, stateKey);\n        }\n        children.push(child);\n        bfcacheEntry = bfcacheEntry.next;\n    }while (bfcacheEntry !== null);\n    return children;\n}\n\n//# sourceMappingURL=layout-router.js.map", "'use client';\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { useUntrackedPathname } from './navigation-untracked';\nimport { isNextRouterError } from './is-next-router-error';\nimport { handleHardNavError } from './nav-failure-handler';\nimport { HandleISRError } from './handle-isr-error';\nimport { isBot } from '../../shared/lib/router/utils/is-bot';\nconst isBotUserAgent = typeof window !== 'undefined' && isBot(window.navigator.userAgent);\nexport class ErrorBoundaryHandler extends React.Component {\n    static getDerivedStateFromError(error) {\n        if (isNextRouterError(error)) {\n            // Re-throw if an expected internal Next.js router error occurs\n            // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n            throw error;\n        }\n        return {\n            error\n        };\n    }\n    static getDerivedStateFromProps(props, state) {\n        const { error } = state;\n        // if we encounter an error while\n        // a navigation is pending we shouldn't render\n        // the error boundary and instead should fallback\n        // to a hard navigation to attempt recovering\n        if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n            if (error && handleHardNavError(error)) {\n                // clear error so we don't render anything\n                return {\n                    error: null,\n                    previousPathname: props.pathname\n                };\n            }\n        }\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.error) {\n            return {\n                error: null,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            error: state.error,\n            previousPathname: props.pathname\n        };\n    }\n    // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n    render() {\n        //When it's bot request, segment level error boundary will keep rendering the children,\n        // the final error will be caught by the root error boundary and determine wether need to apply graceful degrade.\n        if (this.state.error && !isBotUserAgent) {\n            return /*#__PURE__*/ _jsxs(_Fragment, {\n                children: [\n                    /*#__PURE__*/ _jsx(HandleISRError, {\n                        error: this.state.error\n                    }),\n                    this.props.errorStyles,\n                    this.props.errorScripts,\n                    /*#__PURE__*/ _jsx(this.props.errorComponent, {\n                        error: this.state.error,\n                        reset: this.reset\n                    })\n                ]\n            });\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props), this.reset = ()=>{\n            this.setState({\n                error: null\n            });\n        };\n        this.state = {\n            error: null,\n            previousPathname: this.props.pathname\n        };\n    }\n}\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */ /**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */ export function ErrorBoundary(param) {\n    let { errorComponent, errorStyles, errorScripts, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these errors can occur), we will get the correct pathname.\n    const pathname = useUntrackedPathname();\n    if (errorComponent) {\n        return /*#__PURE__*/ _jsx(ErrorBoundaryHandler, {\n            pathname: pathname,\n            errorComponent: errorComponent,\n            errorStyles: errorStyles,\n            errorScripts: errorScripts,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ _jsx(_Fragment, {\n        children: children\n    });\n}\n\n//# sourceMappingURL=error-boundary.js.map", "import { warnOnce } from '../../utils/warn-once';\n/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */ export function disableSmoothScrollDuringRouteTransition(fn, options) {\n    if (options === void 0) options = {};\n    // if only the hash is changed, we don't need to disable smooth scrolling\n    // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n    if (options.onlyHashChange) {\n        fn();\n        return;\n    }\n    const htmlElement = document.documentElement;\n    const hasDataAttribute = htmlElement.dataset.scrollBehavior === 'smooth';\n    // Since this is a breaking change, this is temporarily flagged\n    // and will be false by default.\n    // In the next major (v16), this will be automatically enabled\n    if (process.env.__NEXT_OPTIMIZE_ROUTER_SCROLL) {\n        if (!hasDataAttribute) {\n            // No smooth scrolling configured, run directly without style manipulation\n            fn();\n            return;\n        }\n    } else {\n        // Old behavior: always manipulate styles, but warn about upcoming change\n        // Warn if smooth scrolling is detected but no data attribute is present\n        if (process.env.NODE_ENV === 'development' && !hasDataAttribute && getComputedStyle(htmlElement).scrollBehavior === 'smooth') {\n            warnOnce('Detected `scroll-behavior: smooth` on the `<html>` element. In a future version, ' + 'Next.js will no longer automatically disable smooth scrolling during route transitions. ' + 'To prepare for this change, add `data-scroll-behavior=\"smooth\"` to your <html> element. ' + 'Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior');\n        }\n    }\n    // Proceed with temporarily disabling smooth scrolling\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = 'auto';\n    if (!options.dontForceLayout) {\n        // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n        // Otherwise it will not pickup the change in scrollBehavior\n        // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n        htmlElement.getClientRects();\n    }\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n}\n\n//# sourceMappingURL=disable-smooth-scroll.js.map", "import { RedirectStatusCode } from './redirect-status-code';\nimport { RedirectType, isRedirectError, REDIRECT_ERROR_CODE } from './redirect-error';\nconst actionAsyncStorage = typeof window === 'undefined' ? require('../../server/app-render/action-async-storage.external').actionAsyncStorage : undefined;\nexport function getRedirectError(url, type, statusCode) {\n    if (statusCode === void 0) statusCode = RedirectStatusCode.TemporaryRedirect;\n    const error = Object.defineProperty(new Error(REDIRECT_ERROR_CODE), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = REDIRECT_ERROR_CODE + \";\" + type + \";\" + url + \";\" + statusCode + \";\";\n    return error;\n}\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */ export function redirect(/** The URL to redirect to */ url, type) {\n    var _actionAsyncStorage_getStore;\n    type != null ? type : type = (actionAsyncStorage == null ? void 0 : (_actionAsyncStorage_getStore = actionAsyncStorage.getStore()) == null ? void 0 : _actionAsyncStorage_getStore.isAction) ? RedirectType.push : RedirectType.replace;\n    throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect);\n}\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */ export function permanentRedirect(/** The URL to redirect to */ url, type) {\n    if (type === void 0) type = RedirectType.replace;\n    throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect);\n}\nexport function getURLFromRedirectError(error) {\n    if (!isRedirectError(error)) return null;\n    // Slices off the beginning of the digest that contains the code and the\n    // separating ';'.\n    return error.digest.split(';').slice(2, -2).join(';');\n}\nexport function getRedirectTypeFromError(error) {\n    if (!isRedirectError(error)) {\n        throw Object.defineProperty(new Error('Not a redirect error'), \"__NEXT_ERROR_CODE\", {\n            value: \"E260\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return error.digest.split(';', 2)[1];\n}\nexport function getRedirectStatusCodeFromError(error) {\n    if (!isRedirectError(error)) {\n        throw Object.defineProperty(new Error('Not a redirect error'), \"__NEXT_ERROR_CODE\", {\n            value: \"E260\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return Number(error.digest.split(';').at(-2));\n}\n\n//# sourceMappingURL=redirect.js.map", "import { HTTP_ERROR_FALLBACK_ERROR_CODE } from './http-access-fallback/http-access-fallback';\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */ const DIGEST = \"\" + HTTP_ERROR_FALLBACK_ERROR_CODE + \";404\";\nexport function notFound() {\n    // eslint-disable-next-line no-throw-literal\n    const error = Object.defineProperty(new Error(DIGEST), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = DIGEST;\n    throw error;\n}\n\n//# sourceMappingURL=not-found.js.map", "export const RSC_HEADER = 'rsc';\nexport const ACTION_HEADER = 'next-action';\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'next-router-state-tree';\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'next-router-prefetch';\n// This contains the path to the segment being prefetched.\n// TODO: If we change next-router-state-tree to be a segment path, we can use\n// that instead. Then next-router-prefetch and next-router-segment-prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER = 'next-router-segment-prefetch';\nexport const NEXT_HMR_REFRESH_HEADER = 'next-hmr-refresh';\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__';\nexport const NEXT_URL = 'next-url';\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component';\nexport const FLIGHT_HEADERS = [\n    RSC_HEADER,\n    NEXT_ROUTER_STATE_TREE_HEADER,\n    NEXT_ROUTER_PREFETCH_HEADER,\n    NEXT_HMR_REFRESH_HEADER,\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n];\nexport const NEXT_RSC_UNION_QUERY = '_rsc';\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time';\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed';\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path';\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query';\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender';\nexport const NEXT_ACTION_NOT_FOUND_HEADER = 'x-nextjs-action-not-found';\n\n//# sourceMappingURL=app-router-headers.js.map", "export const matchSegment = (existingSegment, segment)=>{\n    // segment is either Array or string\n    if (typeof existingSegment === 'string') {\n        if (typeof segment === 'string') {\n            // Common case: segment is just a string\n            return existingSegment === segment;\n        }\n        return false;\n    }\n    if (typeof segment === 'string') {\n        return false;\n    }\n    return existingSegment[0] === segment[0] && existingSegment[1] === segment[1];\n};\n\n//# sourceMappingURL=match-segments.js.map", "export const ACTION_REFRESH = 'refresh';\nexport const ACTION_NAVIGATE = 'navigate';\nexport const ACTION_RESTORE = 'restore';\nexport const ACTION_SERVER_PATCH = 'server-patch';\nexport const ACTION_PREFETCH = 'prefetch';\nexport const ACTION_HMR_REFRESH = 'hmr-refresh';\nexport const ACTION_SERVER_ACTION = 'server-action';\n/**\n * PrefetchKind defines the type of prefetching that should be done.\n * - `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully.\n * - `full` - prefetch the page data fully.\n * - `temporary` - a temporary prefetch entry is added to the cache, this is used when prefetch={false} is used in next/link or when you push a route programmatically.\n */ export var PrefetchKind = /*#__PURE__*/ function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n    return PrefetchKind;\n}({});\nexport var PrefetchCacheEntryStatus = /*#__PURE__*/ function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n    return PrefetchCacheEntryStatus;\n}({});\n\n//# sourceMappingURL=router-reducer-types.js.map", "export function getSegmentValue(segment) {\n    return Array.isArray(segment) ? segment[1] : segment;\n}\n\n//# sourceMappingURL=get-segment-value.js.map", "export function createHrefFromUrl(url, includeHash) {\n    if (includeHash === void 0) includeHash = true;\n    return url.pathname + url.search + (includeHash ? url.hash : '');\n}\n\n//# sourceMappingURL=create-href-from-url.js.map", "export function isGroupSegment(segment) {\n    // Use array[0] for performant purpose\n    return segment[0] === '(' && segment.endsWith(')');\n}\nexport function isParallelRouteSegment(segment) {\n    return segment.startsWith('@') && segment !== '@children';\n}\nexport function addSearchParamsIfPageSegment(segment, searchParams) {\n    const isPageSegment = segment.includes(PAGE_SEGMENT_KEY);\n    if (isPageSegment) {\n        const stringifiedQuery = JSON.stringify(searchParams);\n        return stringifiedQuery !== '{}' ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery : PAGE_SEGMENT_KEY;\n    }\n    return segment;\n}\nexport const PAGE_SEGMENT_KEY = '__PAGE__';\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__';\n\n//# sourceMappingURL=segment.js.map", "export class UnrecognizedActionError extends Error {\n    constructor(...args){\n        super(...args);\n        this.name = 'UnrecognizedActionError';\n    }\n}\n/**\n * Check whether a server action call failed because the server action was not recognized by the server.\n * This can happen if the client and the server are not from the same deployment.\n *\n * Example usage:\n * ```ts\n * try {\n *   await myServerAction();\n * } catch (err) {\n *   if (unstable_isUnrecognizedActionError(err)) {\n *     // The client is from a different deployment than the server.\n *     // Reloading the page will fix this mismatch.\n *     window.alert(\"Please refresh the page and try again\");\n *     return;\n *   }\n * }\n * ```\n * */ export function unstable_isUnrecognizedActionError(error) {\n    return !!(error && typeof error === 'object' && error instanceof UnrecognizedActionError);\n}\n\n//# sourceMappingURL=unrecognized-action-error.js.map", "import { HTTP_ERROR_FALLBACK_ERROR_CODE } from './http-access-fallback/http-access-fallback';\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */ const DIGEST = \"\" + HTTP_ERROR_FALLBACK_ERROR_CODE + \";403\";\nexport function forbidden() {\n    if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n        throw Object.defineProperty(new Error(\"`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E488\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // eslint-disable-next-line no-throw-literal\n    const error = Object.defineProperty(new Error(DIGEST), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = DIGEST;\n    throw error;\n}\n\n//# sourceMappingURL=forbidden.js.map", "import { HTTP_ERROR_FALLBACK_ERROR_CODE } from './http-access-fallback/http-access-fallback';\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */ const DIGEST = \"\" + HTTP_ERROR_FALLBACK_ERROR_CODE + \";401\";\nexport function unauthorized() {\n    if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n        throw Object.defineProperty(new Error(\"`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E411\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // eslint-disable-next-line no-throw-literal\n    const error = Object.defineProperty(new Error(DIGEST), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = DIGEST;\n    throw error;\n}\n\n//# sourceMappingURL=unauthorized.js.map", "import { HTML_LIMITED_BOT_UA_RE } from './html-bots';\n// Bot crawler that will spin up a headless browser and execute JS.\n// Only the main Googlebot search crawler executes JavaScript, not other Google crawlers.\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\n// This regex specifically matches \"Googlebot\" but NOT \"Mediapartners-Google\", \"AdsBot-Google\", etc.\nconst HEADLESS_BROWSER_BOT_UA_RE = /Googlebot(?!-)|Googlebot$/i;\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source;\nexport { HTML_LIMITED_BOT_UA_RE };\nfunction isDomBotUA(userAgent) {\n    return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent);\n}\nfunction isHtmlLimitedBotUA(userAgent) {\n    return HTML_LIMITED_BOT_UA_RE.test(userAgent);\n}\nexport function isBot(userAgent) {\n    return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent);\n}\nexport function getBotType(userAgent) {\n    if (isDomBotUA(userAgent)) {\n        return 'dom';\n    }\n    if (isHtmlLimitedBotUA(userAgent)) {\n        return 'html';\n    }\n    return undefined;\n}\n\n//# sourceMappingURL=is-bot.js.map", "import { useState } from 'react';\n// When the flag is disabled, only track the currently active tree\nconst MAX_BF_CACHE_ENTRIES = process.env.__NEXT_ROUTER_BF_CACHE ? 3 : 1;\n/**\n * Keeps track of the most recent N trees (FlightRouterStates) that were active\n * at a certain segment level. E.g. for a segment \"/a/b/[param]\", this hook\n * tracks the last N param values that the router rendered for N.\n *\n * The result of this hook precisely determines the number and order of\n * trees that are rendered in parallel at their segment level.\n *\n * The purpose of this cache is to we can preserve the React and DOM state of\n * some number of inactive trees, by rendering them in an <Activity> boundary.\n * That means it would not make sense for the the lifetime of the cache to be\n * any longer than the lifetime of the React tree; e.g. if the hook were\n * unmounted, then the React tree would be, too. So, we use React state to\n * manage it.\n *\n * Note that we don't store the RSC data for the cache entries in this hook —\n * the data for inactive segments is stored in the parent CacheNode, which\n * *does* have a longer lifetime than the React tree. This hook only determines\n * which of those trees should have their *state* preserved, by <Activity>.\n */ export function useRouterBFCache(activeTree, activeStateKey) {\n    // The currently active entry. The entries form a linked list, sorted in\n    // order of most recently active. This allows us to reuse parts of the list\n    // without cloning, unless there's a reordering or removal.\n    // TODO: Once we start tracking back/forward history at each route level,\n    // we should use the history order instead. In other words, when traversing\n    // to an existing entry as a result of a popstate event, we should maintain\n    // the existing order instead of moving it to the front of the list. I think\n    // an initial implementation of this could be to pass an incrementing id\n    // to history.pushState/replaceState, then use that here for ordering.\n    const [prevActiveEntry, setPrevActiveEntry] = useState(()=>{\n        const initialEntry = {\n            tree: activeTree,\n            stateKey: activeStateKey,\n            next: null\n        };\n        return initialEntry;\n    });\n    if (prevActiveEntry.tree === activeTree) {\n        // Fast path. The active tree hasn't changed, so we can reuse the\n        // existing state.\n        return prevActiveEntry;\n    }\n    // The route tree changed. Note that this doesn't mean that the tree changed\n    // *at this level* — the change may be due to a child route. Either way, we\n    // need to either add or update the router tree in the bfcache.\n    //\n    // The rest of the code looks more complicated than it actually is because we\n    // can't mutate the state in place; we have to copy-on-write.\n    // Create a new entry for the active cache key. This is the head of the new\n    // linked list.\n    const newActiveEntry = {\n        tree: activeTree,\n        stateKey: activeStateKey,\n        next: null\n    };\n    // We need to append the old list onto the new list. If the head of the new\n    // list was already present in the cache, then we'll need to clone everything\n    // that came before it. Then we can reuse the rest.\n    let n = 1;\n    let oldEntry = prevActiveEntry;\n    let clonedEntry = newActiveEntry;\n    while(oldEntry !== null && n < MAX_BF_CACHE_ENTRIES){\n        if (oldEntry.stateKey === activeStateKey) {\n            // Fast path. This entry in the old list that corresponds to the key that\n            // is now active. We've already placed a clone of this entry at the front\n            // of the new list. We can reuse the rest of the old list without cloning.\n            // NOTE: We don't need to worry about eviction in this case because we\n            // haven't increased the size of the cache, and we assume the max size\n            // is constant across renders. If we were to change it to a dynamic limit,\n            // then the implementation would need to account for that.\n            clonedEntry.next = oldEntry.next;\n            break;\n        } else {\n            // Clone the entry and append it to the list.\n            n++;\n            const entry = {\n                tree: oldEntry.tree,\n                stateKey: oldEntry.stateKey,\n                next: null\n            };\n            clonedEntry.next = entry;\n            clonedEntry = entry;\n        }\n        oldEntry = oldEntry.next;\n    }\n    setPrevActiveEntry(newActiveEntry);\n    return newActiveEntry;\n}\n\n//# sourceMappingURL=bfcache.js.map", "import { hexHash } from '../../hash';\nexport function computeCacheBustingSearchParam(prefetchHeader, segmentPrefetchHeader, stateTreeHeader, nextUrlHeader) {\n    if ((prefetchHeader === undefined || prefetchHeader === '0') && segmentPrefetchHeader === undefined && stateTreeHeader === undefined && nextUrlHeader === undefined) {\n        return '';\n    }\n    return hexHash([\n        prefetchHeader || '0',\n        segmentPrefetchHeader || '0',\n        stateTreeHeader || '0',\n        nextUrlHeader || '0'\n    ].join(','));\n}\n\n//# sourceMappingURL=cache-busting-search-param.js.map", "import { PAGE_SEGMENT_KEY } from '../segment';\nexport const ROOT_SEGMENT_REQUEST_KEY = '';\nexport const ROOT_SEGMENT_CACHE_KEY = '';\nexport function createSegmentRequestKeyPart(segment) {\n    if (typeof segment === 'string') {\n        if (segment.startsWith(PAGE_SEGMENT_KEY)) {\n            // The Flight Router State type sometimes includes the search params in\n            // the page segment. However, the Segment Cache tracks this as a separate\n            // key. So, we strip the search params here, and then add them back when\n            // the cache entry is turned back into a FlightRouterState. This is an\n            // unfortunate consequence of the FlightRouteState being used both as a\n            // transport type and as a cache key; we'll address this once more of the\n            // Segment Cache implementation has settled.\n            // TODO: We should hoist the search params out of the FlightRouterState\n            // type entirely, This is our plan for dynamic route params, too.\n            return PAGE_SEGMENT_KEY;\n        }\n        const safeName = // TODO: FlightRouterState encodes Not Found routes as \"/_not-found\".\n        // But params typically don't include the leading slash. We should use\n        // a different encoding to avoid this special case.\n        segment === '/_not-found' ? '_not-found' : encodeToFilesystemAndURLSafeString(segment);\n        // Since this is not a dynamic segment, it's fully encoded. It does not\n        // need to be \"hydrated\" with a param value.\n        return safeName;\n    }\n    const name = segment[0];\n    const paramType = segment[2];\n    const safeName = encodeToFilesystemAndURLSafeString(name);\n    const encodedName = '$' + paramType + '$' + safeName;\n    return encodedName;\n}\nexport function appendSegmentRequestKeyPart(parentRequestKey, parallelRouteKey, childRequestKeyPart) {\n    // Aside from being filesystem safe, segment keys are also designed so that\n    // each segment and parallel route creates its own subdirectory. Roughly in\n    // the same shape as the source app directory. This is mostly just for easier\n    // debugging (you can open up the build folder and navigate the output); if\n    // we wanted to do we could just use a flat structure.\n    // Omit the parallel route key for children, since this is the most\n    // common case. Saves some bytes (and it's what the app directory does).\n    const slotKey = parallelRouteKey === 'children' ? childRequestKeyPart : \"@\" + encodeToFilesystemAndURLSafeString(parallelRouteKey) + \"/\" + childRequestKeyPart;\n    return parentRequestKey + '/' + slotKey;\n}\nexport function createSegmentCacheKeyPart(requestKeyPart, segment) {\n    if (typeof segment === 'string') {\n        return requestKeyPart;\n    }\n    const paramValue = segment[1];\n    const safeValue = encodeToFilesystemAndURLSafeString(paramValue);\n    return requestKeyPart + '$' + safeValue;\n}\nexport function appendSegmentCacheKeyPart(parentSegmentKey, parallelRouteKey, childCacheKeyPart) {\n    const slotKey = parallelRouteKey === 'children' ? childCacheKeyPart : \"@\" + encodeToFilesystemAndURLSafeString(parallelRouteKey) + \"/\" + childCacheKeyPart;\n    return parentSegmentKey + '/' + slotKey;\n}\n// Define a regex pattern to match the most common characters found in a route\n// param. It excludes anything that might not be cross-platform filesystem\n// compatible, like |. It does not need to be precise because the fallback is to\n// just base64url-encode the whole parameter, which is fine; we just don't do it\n// by default for compactness, and for easier debugging.\nconst simpleParamValueRegex = /^[a-zA-Z0-9\\-_@]+$/;\nfunction encodeToFilesystemAndURLSafeString(value) {\n    if (simpleParamValueRegex.test(value)) {\n        return value;\n    }\n    // If there are any unsafe characters, base64url-encode the entire value.\n    // We also add a ! prefix so it doesn't collide with the simple case.\n    const base64url = btoa(value).replace(/\\+/g, '-') // Replace '+' with '-'\n    .replace(/\\//g, '_') // Replace '/' with '_'\n    .replace(/=+$/, '') // Remove trailing '='\n    ;\n    return '!' + base64url;\n}\nexport function convertSegmentPathToStaticExportFilename(segmentPath) {\n    return \"__next\" + segmentPath.replace(/\\//g, '.') + \".txt\";\n}\n\n//# sourceMappingURL=segment-value-encoding.js.map", "import { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment';\nexport function createRouter<PERSON><PERSON><PERSON><PERSON>(segment, withoutSearchParameters) {\n    if (withoutSearchParameters === void 0) withoutSearchParameters = false;\n    // if the segment is an array, it means it's a dynamic segment\n    // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n    if (Array.isArray(segment)) {\n        return segment[0] + \"|\" + segment[1] + \"|\" + segment[2];\n    }\n    // Page segments might have search parameters, ie __PAGE__?foo=bar\n    // When `withoutSearchParameters` is true, we only want to return the page segment\n    if (withoutSearchParameters && segment.startsWith(PAGE_SEGMENT_KEY)) {\n        return PAGE_SEGMENT_KEY;\n    }\n    return segment;\n}\n\n//# sourceMappingURL=create-router-cache-key.js.map", "import { isInterceptionRouteAppPath } from '../../../../shared/lib/router/utils/interception-routes';\nexport function hasInterceptionRouteInCurrentTree(param) {\n    let [segment, parallelRoutes] = param;\n    // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n    if (Array.isArray(segment) && (segment[2] === 'di' || segment[2] === 'ci')) {\n        return true;\n    }\n    // If segment is not an array, apply the existing string-based check\n    if (typeof segment === 'string' && isInterceptionRouteAppPath(segment)) {\n        return true;\n    }\n    // Iterate through parallelRoutes if they exist\n    if (parallelRoutes) {\n        for(const key in parallelRoutes){\n            if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n//# sourceMappingURL=has-interception-route-in-current-tree.js.map", "import { useContext, useMemo } from 'react';\nimport { AppRouterContext, LayoutRouterContext } from '../../shared/lib/app-router-context.shared-runtime';\nimport { SearchParamsContext, PathnameContext, PathParamsContext } from '../../shared/lib/hooks-client-context.shared-runtime';\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value';\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment';\nimport { ReadonlyURLSearchParams } from './navigation.react-server';\nconst useDynamicRouteParams = typeof window === 'undefined' ? require('../../server/app-render/dynamic-rendering').useDynamicRouteParams : undefined;\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */ // Client components API\nexport function useSearchParams() {\n    const searchParams = useContext(SearchParamsContext);\n    // In the case where this is `null`, the compat types added in\n    // `next-env.d.ts` will add a new overload that changes the return type to\n    // include `null`.\n    const readonlySearchParams = useMemo(()=>{\n        if (!searchParams) {\n            // When the router is not ready in pages, we won't have the search params\n            // available.\n            return null;\n        }\n        return new ReadonlyURLSearchParams(searchParams);\n    }, [\n        searchParams\n    ]);\n    if (typeof window === 'undefined') {\n        // AsyncLocalStorage should not be included in the client bundle.\n        const { bailoutToClientRendering } = require('./bailout-to-client-rendering');\n        // TODO-APP: handle dynamic = 'force-static' here and on the client\n        bailoutToClientRendering('useSearchParams()');\n    }\n    return readonlySearchParams;\n}\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */ // Client components API\nexport function usePathname() {\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('usePathname()');\n    // In the case where this is `null`, the compat types added in `next-env.d.ts`\n    // will add a new overload that changes the return type to include `null`.\n    return useContext(PathnameContext);\n}\n// Client components API\nexport { ServerInsertedHTMLContext, useServerInsertedHTML } from '../../shared/lib/server-inserted-html.shared-runtime';\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */ // Client components API\nexport function useRouter() {\n    const router = useContext(AppRouterContext);\n    if (router === null) {\n        throw Object.defineProperty(new Error('invariant expected app router to be mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E238\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return router;\n}\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */ // Client components API\nexport function useParams() {\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('useParams()');\n    return useContext(PathParamsContext);\n}\n/** Get the canonical parameters from the current level to the leaf node. */ // Client components API\nfunction getSelectedLayoutSegmentPath(tree, parallelRouteKey, first, segmentPath) {\n    if (first === void 0) first = true;\n    if (segmentPath === void 0) segmentPath = [];\n    let node;\n    if (first) {\n        // Use the provided parallel route key on the first parallel route\n        node = tree[1][parallelRouteKey];\n    } else {\n        // After first parallel route prefer children, if there's no children pick the first parallel route.\n        const parallelRoutes = tree[1];\n        var _parallelRoutes_children;\n        node = (_parallelRoutes_children = parallelRoutes.children) != null ? _parallelRoutes_children : Object.values(parallelRoutes)[0];\n    }\n    if (!node) return segmentPath;\n    const segment = node[0];\n    let segmentValue = getSegmentValue(segment);\n    if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n        return segmentPath;\n    }\n    segmentPath.push(segmentValue);\n    return getSelectedLayoutSegmentPath(node, parallelRouteKey, false, segmentPath);\n}\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */ // Client components API\nexport function useSelectedLayoutSegments(parallelRouteKey) {\n    if (parallelRouteKey === void 0) parallelRouteKey = 'children';\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('useSelectedLayoutSegments()');\n    const context = useContext(LayoutRouterContext);\n    // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n    if (!context) return null;\n    return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey);\n}\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */ // Client components API\nexport function useSelectedLayoutSegment(parallelRouteKey) {\n    if (parallelRouteKey === void 0) parallelRouteKey = 'children';\n    useDynamicRouteParams == null ? void 0 : useDynamicRouteParams('useSelectedLayoutSegment()');\n    const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey);\n    if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n        return null;\n    }\n    const selectedLayoutSegment = parallelRouteKey === 'children' ? selectedLayoutSegments[0] : selectedLayoutSegments[selectedLayoutSegments.length - 1];\n    // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n    // and returning an internal value like `__DEFAULT__` would be confusing.\n    return selectedLayoutSegment === DEFAULT_SEGMENT_KEY ? null : selectedLayoutSegment;\n}\nexport { unstable_isUnrecognizedActionError } from './unrecognized-action-error';\n// Shared components APIs\nexport { notFound, forbidden, unauthorized, redirect, permanentRedirect, RedirectType, ReadonlyURLSearchParams, unstable_rethrow } from './navigation.react-server';\n\n//# sourceMappingURL=navigation.js.map", "import React, { use } from 'react';\nimport { isThenable } from '../../shared/lib/is-thenable';\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch = null;\nexport function dispatchAppRouterAction(action) {\n    if (dispatch === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    dispatch(action);\n}\nexport function useActionQueue(actionQueue) {\n    const [state, setState] = React.useState(actionQueue.state);\n    // Because of a known issue that requires to decode Flight streams inside the\n    // render phase, we have to be a bit clever and assign the dispatch method to\n    // a module-level variable upon initialization. The useState hook in this\n    // module only exists to synchronize state that lives outside of React.\n    // Ideally, what we'd do instead is pass the state as a prop to root.render;\n    // this is conceptually how we're modeling the app router state, despite the\n    // weird implementation details.\n    if (process.env.NODE_ENV !== 'production') {\n        const { useAppDevRenderingIndicator } = require('../../next-devtools/userspace/use-app-dev-rendering-indicator');\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const appDevRenderingIndicator = useAppDevRenderingIndicator();\n        dispatch = (action)=>{\n            appDevRenderingIndicator(()=>{\n                actionQueue.dispatch(action, setState);\n            });\n        };\n    } else {\n        dispatch = (action)=>actionQueue.dispatch(action, setState);\n    }\n    return isThenable(state) ? use(state) : state;\n}\n\n//# sourceMappingURL=use-action-queue.js.map", "import { useEffect } from 'react';\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url';\nexport function handleHardNavError(error) {\n    if (error && typeof window !== 'undefined' && window.next.__pendingUrl && createHrefFromUrl(new URL(window.location.href)) !== createHrefFromUrl(window.next.__pendingUrl)) {\n        console.error(\"Error occurred during navigation, falling back to hard navigation\", error);\n        window.location.href = window.next.__pendingUrl.toString();\n        return true;\n    }\n    return false;\n}\nexport function useNavFailureHandler() {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n        // this if is only for DCE of the feature flag not conditional\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        useEffect(()=>{\n            const uncaughtExceptionHandler = (evt)=>{\n                const error = 'reason' in evt ? evt.reason : evt.error;\n                // if we have an unhandled exception/rejection during\n                // a navigation we fall back to a hard navigation to\n                // attempt recovering to a good state\n                handleHardNavError(error);\n            };\n            window.addEventListener('unhandledrejection', uncaughtExceptionHandler);\n            window.addEventListener('error', uncaughtExceptionHandler);\n            return ()=>{\n                window.removeEventListener('error', uncaughtExceptionHandler);\n                window.removeEventListener('unhandledrejection', uncaughtExceptionHandler);\n            };\n        }, []);\n    }\n}\n\n//# sourceMappingURL=nav-failure-handler.js.map", "import { normalizeAppPath } from './app-paths';\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n    '(..)(..)',\n    '(.)',\n    '(..)',\n    '(...)'\n];\nexport function isInterceptionRouteAppPath(path) {\n    // TODO-APP: add more serious validation\n    return path.split('/').find((segment)=>INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m))) !== undefined;\n}\nexport function extractInterceptionRouteInformation(path) {\n    let interceptingRoute, marker, interceptedRoute;\n    for (const segment of path.split('/')){\n        marker = INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m));\n        if (marker) {\n            ;\n            [interceptingRoute, interceptedRoute] = path.split(marker, 2);\n            break;\n        }\n    }\n    if (!interceptingRoute || !marker || !interceptedRoute) {\n        throw Object.defineProperty(new Error(\"Invalid interception route: \" + path + \". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E269\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n    ;\n    switch(marker){\n        case '(.)':\n            // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n            if (interceptingRoute === '/') {\n                interceptedRoute = \"/\" + interceptedRoute;\n            } else {\n                interceptedRoute = interceptingRoute + '/' + interceptedRoute;\n            }\n            break;\n        case '(..)':\n            // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n            if (interceptingRoute === '/') {\n                throw Object.defineProperty(new Error(\"Invalid interception route: \" + path + \". Cannot use (..) marker at the root level, use (.) instead.\"), \"__NEXT_ERROR_CODE\", {\n                    value: \"E207\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            interceptedRoute = interceptingRoute.split('/').slice(0, -1).concat(interceptedRoute).join('/');\n            break;\n        case '(...)':\n            // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n            interceptedRoute = '/' + interceptedRoute;\n            break;\n        case '(..)(..)':\n            // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n            const splitInterceptingRoute = interceptingRoute.split('/');\n            if (splitInterceptingRoute.length <= 2) {\n                throw Object.defineProperty(new Error(\"Invalid interception route: \" + path + \". Cannot use (..)(..) marker at the root level or one level up.\"), \"__NEXT_ERROR_CODE\", {\n                    value: \"E486\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            interceptedRoute = splitInterceptingRoute.slice(0, -2).concat(interceptedRoute).join('/');\n            break;\n        default:\n            throw Object.defineProperty(new Error('Invariant: unexpected marker'), \"__NEXT_ERROR_CODE\", {\n                value: \"E112\",\n                enumerable: false,\n                configurable: true\n            });\n    }\n    return {\n        interceptingRoute,\n        interceptedRoute\n    };\n}\n\n//# sourceMappingURL=interception-routes.js.map", "/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */ export const unresolvedThenable = {\n    then: ()=>{}\n};\n\n//# sourceMappingURL=unresolved-thenable.js.map", "import { addSearchParamsIfPageSegment, DEFAULT_SEGMENT_KEY, PAGE_SEGMENT_KEY } from '../shared/lib/segment';\nimport { ROOT_SEGMENT_REQUEST_KEY } from '../shared/lib/segment-cache/segment-value-encoding';\nimport { NEXT_REWRITTEN_PATH_HEADER, NEXT_REWRITTEN_QUERY_HEADER, NEXT_RSC_UNION_QUERY } from './components/app-router-headers';\nexport function getRenderedSearch(response) {\n    // If the server performed a rewrite, the search params used to render the\n    // page will be different from the params in the request URL. In this case,\n    // the response will include a header that gives the rewritten search query.\n    const rewrittenQuery = response.headers.get(NEXT_REWRITTEN_QUERY_HEADER);\n    if (rewrittenQuery !== null) {\n        return rewrittenQuery === '' ? '' : '?' + rewrittenQuery;\n    }\n    // If the header is not present, there was no rewrite, so we use the search\n    // query of the response URL.\n    return urlToUrlWithoutFlightMarker(new URL(response.url)).search;\n}\nexport function getRenderedPathname(response) {\n    // If the server performed a rewrite, the pathname used to render the\n    // page will be different from the pathname in the request URL. In this case,\n    // the response will include a header that gives the rewritten pathname.\n    const rewrittenPath = response.headers.get(NEXT_REWRITTEN_PATH_HEADER);\n    return rewrittenPath != null ? rewrittenPath : urlToUrlWithoutFlightMarker(new URL(response.url)).pathname;\n}\nexport function parseDynamicParamFromURLPart(paramType, pathnameParts, partIndex) {\n    // This needs to match the behavior in get-dynamic-param.ts.\n    switch(paramType){\n        // Catchalls\n        case 'c':\n        case 'ci':\n            {\n                // Catchalls receive all the remaining URL parts. If there are no\n                // remaining pathname parts, return an empty array.\n                return partIndex < pathnameParts.length ? pathnameParts.slice(partIndex).map((s)=>encodeURIComponent(s)) : [];\n            }\n        // Optional catchalls\n        case 'oc':\n            {\n                // Optional catchalls receive all the remaining URL parts, unless this is\n                // the end of the pathname, in which case they return null.\n                return partIndex < pathnameParts.length ? pathnameParts.slice(partIndex).map((s)=>encodeURIComponent(s)) : null;\n            }\n        // Dynamic\n        case 'd':\n        case 'di':\n            {\n                if (partIndex >= pathnameParts.length) {\n                    // The route tree expected there to be more parts in the URL than there\n                    // actually are. This could happen if the x-nextjs-rewritten-path header\n                    // is incorrectly set, or potentially due to bug in Next.js. TODO:\n                    // Should this be a hard error? During a prefetch, we can just abort.\n                    // During a client navigation, we could trigger a hard refresh. But if\n                    // it happens during initial render, we don't really have any\n                    // recovery options.\n                    return '';\n                }\n                return encodeURIComponent(pathnameParts[partIndex]);\n            }\n        default:\n            paramType;\n            return '';\n    }\n}\nexport function doesStaticSegmentAppearInURL(segment) {\n    // This is not a parameterized segment; however, we need to determine\n    // whether or not this segment appears in the URL. For example, this route\n    // groups do not appear in the URL, so they should be skipped. Any other\n    // special cases must be handled here.\n    // TODO: Consider encoding this directly into the router tree instead of\n    // inferring it on the client based on the segment type. Something like\n    // a `doesAppearInURL` flag in FlightRouterState.\n    if (segment === ROOT_SEGMENT_REQUEST_KEY || // For some reason, the loader tree sometimes includes extra __PAGE__\n    // \"layouts\" when part of a parallel route. But it's not a leaf node.\n    // Otherwise, we wouldn't need this special case because pages are\n    // always leaf nodes.\n    // TODO: Investigate why the loader produces these fake page segments.\n    segment.startsWith(PAGE_SEGMENT_KEY) || // Route groups.\n    segment[0] === '(' && segment.endsWith(')') || segment === DEFAULT_SEGMENT_KEY || segment === '/_not-found') {\n        return false;\n    } else {\n        // All other segment types appear in the URL\n        return true;\n    }\n}\nexport function getCacheKeyForDynamicParam(paramValue, renderedSearch) {\n    // This needs to match the logic in get-dynamic-param.ts, until we're able to\n    // unify the various implementations so that these are always computed on\n    // the client.\n    if (typeof paramValue === 'string') {\n        // TODO: Refactor or remove this helper function to accept a string rather\n        // than the whole segment type. Also we can probably just append the\n        // search string instead of turning it into JSON.\n        const pageSegmentWithSearchParams = addSearchParamsIfPageSegment(paramValue, Object.fromEntries(new URLSearchParams(renderedSearch)));\n        return pageSegmentWithSearchParams;\n    } else if (paramValue === null) {\n        return '';\n    } else {\n        return paramValue.join('/');\n    }\n}\nexport function urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url);\n    urlWithoutFlightParameters.searchParams.delete(NEXT_RSC_UNION_QUERY);\n    if (process.env.NODE_ENV === 'production') {\n        if (process.env.__NEXT_CONFIG_OUTPUT === 'export' && urlWithoutFlightParameters.pathname.endsWith('.txt')) {\n            const { pathname } = urlWithoutFlightParameters;\n            const length = pathname.endsWith('/index.txt') ? 10 : 4;\n            // Slice off `/index.txt` or `.txt` from the end of the pathname\n            urlWithoutFlightParameters.pathname = pathname.slice(0, -length);\n        }\n    }\n    return urlWithoutFlightParameters;\n}\nexport function getParamValueFromCacheKey(paramCacheKey, paramType) {\n    // Turn the cache key string sent by the server (as part of FlightRouterState)\n    // into a value that can be passed to `useParams` and client components.\n    const isCatchAll = paramType === 'c' || paramType === 'oc';\n    if (isCatchAll) {\n        // Catch-all param keys are a concatenation of the path segments.\n        // See equivalent logic in `getSelectedParams`.\n        // TODO: We should just pass the array directly, rather than concatenate\n        // it to a string and then split it back to an array. It needs to be an\n        // array in some places, like when passing a key React, but we can convert\n        // it at runtime in those places.\n        return paramCacheKey.split('/');\n    }\n    return paramCacheKey;\n}\n\n//# sourceMappingURL=route-params.js.map", "import { startTransition } from 'react';\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types';\nimport { dispatchAppRouterAction } from './components/use-action-queue';\nexport async function callServer(actionId, actionArgs) {\n    return new Promise((resolve, reject)=>{\n        startTransition(()=>{\n            dispatchAppRouterAction({\n                type: ACTION_SERVER_ACTION,\n                actionId,\n                actionArgs,\n                resolve,\n                reject\n            });\n        });\n    });\n}\n\n//# sourceMappingURL=app-call-server.js.map", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */ export function ensureLeadingSlash(path) {\n    return path.startsWith('/') ? path : \"/\" + path;\n}\n\n//# sourceMappingURL=ensure-leading-slash.js.map", "// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\n// Note: The pattern [\\w-]+-Google captures all Google crawlers with \"-Google\" suffix (e.g., Mediapartners-Google, AdsBot-Google, Storebot-Google)\n// as well as crawlers starting with \"Google-\" (e.g., Google-PageRenderer, Google-InspectionTool)\nexport const HTML_LIMITED_BOT_UA_RE = /[\\w-]+-Google|Google-[\\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i;\n\n//# sourceMappingURL=html-bots.js.map", "import { PAGE_SEGMENT_KEY } from '../shared/lib/segment';\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(flightDataPath) {\n    // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n    const flightDataPathLength = 4;\n    // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n    const [tree, seedData, head, isHeadPartial] = flightDataPath.slice(-flightDataPathLength);\n    // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n    const segmentPath = flightDataPath.slice(0, -flightDataPathLength);\n    var _segmentPath_;\n    return {\n        // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n        // to the start of the segment path in some places which makes it hard to use solely the segment path.\n        // Look for \"// TODO-APP: remove ''\" in the codebase.\n        pathToSegment: segmentPath.slice(0, -1),\n        segmentPath,\n        // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n        // in which case we default to ''.\n        segment: (_segmentPath_ = segmentPath[segmentPath.length - 1]) != null ? _segmentPath_ : '',\n        tree,\n        seedData,\n        head,\n        isHeadPartial,\n        isRootRender: flightDataPath.length === flightDataPathLength\n    };\n}\nexport function getNextFlightSegmentPath(flightSegmentPath) {\n    // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n    // to get the next segment path.\n    return flightSegmentPath.slice(2);\n}\nexport function normalizeFlightData(flightData) {\n    // FlightData can be a string when the server didn't respond with a proper flight response,\n    // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n    if (typeof flightData === 'string') {\n        return flightData;\n    }\n    return flightData.map((flightDataPath)=>getFlightDataPartsFromPath(flightDataPath));\n}\n/**\n * This function is used to prepare the flight router state for the request.\n * It removes markers that are not needed by the server, and are purely used\n * for stashing state on the client.\n * @param flightRouterState - The flight router state to prepare.\n * @param isHmrRefresh - Whether this is an HMR refresh request.\n * @returns The prepared flight router state.\n */ export function prepareFlightRouterStateForRequest(flightRouterState, isHmrRefresh) {\n    // HMR requests need the complete, unmodified state for proper functionality\n    if (isHmrRefresh) {\n        return encodeURIComponent(JSON.stringify(flightRouterState));\n    }\n    return encodeURIComponent(JSON.stringify(stripClientOnlyDataFromFlightRouterState(flightRouterState)));\n}\n/**\n * Recursively strips client-only data from FlightRouterState while preserving\n * server-needed information for proper rendering decisions.\n */ function stripClientOnlyDataFromFlightRouterState(flightRouterState) {\n    const [segment, parallelRoutes, _url, refreshMarker, isRootLayout, hasLoadingBoundary] = flightRouterState;\n    // __PAGE__ segments are always fetched from the server, so there's\n    // no need to send them up\n    const cleanedSegment = stripSearchParamsFromPageSegment(segment);\n    // Recursively process parallel routes\n    const cleanedParallelRoutes = {};\n    for (const [key, childState] of Object.entries(parallelRoutes)){\n        cleanedParallelRoutes[key] = stripClientOnlyDataFromFlightRouterState(childState);\n    }\n    const result = [\n        cleanedSegment,\n        cleanedParallelRoutes,\n        null,\n        shouldPreserveRefreshMarker(refreshMarker) ? refreshMarker : null\n    ];\n    // Append optional fields if present\n    if (isRootLayout !== undefined) {\n        result[4] = isRootLayout;\n    }\n    if (hasLoadingBoundary !== undefined) {\n        result[5] = hasLoadingBoundary;\n    }\n    return result;\n}\n/**\n * Strips search parameters from __PAGE__ segments to prevent sensitive\n * client-side data from being sent to the server.\n */ function stripSearchParamsFromPageSegment(segment) {\n    if (typeof segment === 'string' && segment.startsWith(PAGE_SEGMENT_KEY + '?')) {\n        return PAGE_SEGMENT_KEY;\n    }\n    return segment;\n}\n/**\n * Determines whether the refresh marker should be sent to the server\n * Client-only markers like 'refresh' are stripped, while server-needed markers\n * like 'refetch' and 'inside-shared-layout' are preserved.\n */ function shouldPreserveRefreshMarker(refreshMarker) {\n    return Boolean(refreshMarker && refreshMarker !== 'refresh');\n}\n\n//# sourceMappingURL=flight-data-helpers.js.map", "/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */ export function isThenable(promise) {\n    return promise !== null && typeof promise === 'object' && 'then' in promise && typeof promise.then === 'function';\n}\n\n//# sourceMappingURL=is-thenable.js.map", "/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */ export const unstable_rethrow = typeof window === 'undefined' ? require('./unstable-rethrow.server').unstable_rethrow : require('./unstable-rethrow.browser').unstable_rethrow;\n\n//# sourceMappingURL=unstable-rethrow.js.map", "// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str) {\n    let hash = 5381;\n    for(let i = 0; i < str.length; i++){\n        const char = str.charCodeAt(i);\n        hash = (hash << 5) + hash + char & 0xffffffff;\n    }\n    return hash >>> 0;\n}\nexport function hexHash(str) {\n    return djb2Hash(str).toString(36).slice(0, 5);\n}\n\n//# sourceMappingURL=hash.js.map", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash';\nimport { isGroupSegment } from '../../segment';\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */ export function normalizeAppPath(route) {\n    return ensureLeadingSlash(route.split('/').reduce((pathname, segment, index, segments)=>{\n        // Empty segments are ignored.\n        if (!segment) {\n            return pathname;\n        }\n        // Groups are ignored.\n        if (isGroupSegment(segment)) {\n            return pathname;\n        }\n        // Parallel segments are ignored.\n        if (segment[0] === '@') {\n            return pathname;\n        }\n        // The last segment (if it's a leaf) should be ignored.\n        if ((segment === 'page' || segment === 'route') && index === segments.length - 1) {\n            return pathname;\n        }\n        return pathname + \"/\" + segment;\n    }, ''));\n}\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */ export function normalizeRscURL(url) {\n    return url.replace(/\\.rsc($|\\?)/, // $1 ensures `?` is preserved\n    '$1');\n}\n\n//# sourceMappingURL=app-paths.js.map", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n", "import * as React from 'react';\nconst errorRef = {\n    current: null\n};\n// React.cache is currently only available in canary/experimental React channels.\nconst cache = typeof React.cache === 'function' ? React.cache : (fn)=>fn;\n// When Cache Components is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_CACHE_COMPONENTS ? console.error : console.warn;\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n(key)=>{\n    try {\n        logErrorOrWarn(errorRef.current);\n    } finally{\n        errorRef.current = null;\n    }\n});\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */ export function createDedupedByCallsiteServerErrorLoggerDev(getMessage) {\n    return function logDedupedError(...args) {\n        const message = getMessage(...args);\n        if (process.env.NODE_ENV !== 'production') {\n            var _stack;\n            const callStackFrames = (_stack = new Error().stack) == null ? void 0 : _stack.split('\\n');\n            if (callStackFrames === undefined || callStackFrames.length < 4) {\n                logErrorOrWarn(message);\n            } else {\n                // Error:\n                //   logDedupedError\n                //   asyncApiBeingAccessedSynchronously\n                //   <userland callsite>\n                // TODO: This breaks if sourcemaps with ignore lists are enabled.\n                const key = callStackFrames[4];\n                errorRef.current = message;\n                flushCurrentErrorIfNew(key);\n            }\n        } else {\n            logErrorOrWarn(message);\n        }\n    };\n}\n\n//# sourceMappingURL=create-deduped-by-callsite-server-error-logger.js.map", "// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/;\nexport function describeStringPropertyAccess(target, prop) {\n    if (isDefinitelyAValidIdentifier.test(prop)) {\n        return \"`\" + target + \".\" + prop + \"`\";\n    }\n    return \"`\" + target + \"[\" + JSON.stringify(prop) + \"]`\";\n}\nexport function describeHasCheckingStringProperty(target, prop) {\n    const stringifiedProp = JSON.stringify(prop);\n    return \"`Reflect.has(\" + target + \", \" + stringifiedProp + \")`, `\" + stringifiedProp + \" in \" + target + \"`, or similar\";\n}\nexport const wellKnownProperties = new Set([\n    'hasOwnProperty',\n    'isPrototypeOf',\n    'propertyIsEnumerable',\n    'toString',\n    'valueOf',\n    'toLocaleString',\n    // Promise prototype\n    // fallthrough\n    'then',\n    'catch',\n    'finally',\n    // React Promise extension\n    // fallthrough\n    'status',\n    // React introspection\n    'displayName',\n    '_debugInfo',\n    // Common tested properties\n    // fallthrough\n    'toJSON',\n    '$$typeof',\n    '__esModule'\n]);\n\n//# sourceMappingURL=reflect-utils.js.map", "export class ReflectAdapter {\n    static get(target, prop, receiver) {\n        const value = Reflect.get(target, prop, receiver);\n        if (typeof value === 'function') {\n            return value.bind(target);\n        }\n        return value;\n    }\n    static set(target, prop, value, receiver) {\n        return Reflect.set(target, prop, value, receiver);\n    }\n    static has(target, prop) {\n        return Reflect.has(target, prop);\n    }\n    static deleteProperty(target, prop) {\n        return Reflect.deleteProperty(target, prop);\n    }\n}\n\n//# sourceMappingURL=reflect.js.map", "import { StaticGenBailoutError } from '../../client/components/static-generation-bailout';\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external';\nexport function throwWithStaticGenerationBailoutError(route, expression) {\n    throw Object.defineProperty(new StaticGenBailoutError(`Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n        value: \"E576\",\n        enumerable: false,\n        configurable: true\n    });\n}\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(route, expression) {\n    throw Object.defineProperty(new StaticGenBailoutError(`Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n        value: \"E543\",\n        enumerable: false,\n        configurable: true\n    });\n}\nexport function throwForSearchParamsAccessInUseCache(workStore, constructorOpt) {\n    const error = Object.defineProperty(new Error(`Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await \"searchParams\" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n        value: \"E779\",\n        enumerable: false,\n        configurable: true\n    });\n    Error.captureStackTrace(error, constructorOpt);\n    workStore.invalidDynamicUsageError ??= error;\n    throw error;\n}\nexport function isRequestAPICallableInsideAfter() {\n    const afterTaskStore = afterTaskAsyncStorage.getStore();\n    return (afterTaskStore == null ? void 0 : afterTaskStore.rootTaskSpawnPhase) === 'action';\n}\n\n//# sourceMappingURL=utils.js.map", "import { ReflectAdapter } from '../web/spec-extension/adapters/reflect';\nimport { throwToInterruptStaticGeneration, postponeWithTracking, trackDynamicDataInDynamicRender, annotateDynamicAccess, trackSynchronousRequestDataAccessInDev, delayUntilRuntimeStage } from '../app-render/dynamic-rendering';\nimport { workUnitAsyncStorage, throwInvariantForMissingStore } from '../app-render/work-unit-async-storage.external';\nimport { InvariantError } from '../../shared/lib/invariant-error';\nimport { makeDevtoolsIOAwarePromise, makeHangingPromise } from '../dynamic-rendering-utils';\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger';\nimport { describeStringPropertyAccess, describeHasCheckingStringProperty, wellKnownProperties } from '../../shared/lib/utils/reflect-utils';\nimport { throwWithStaticGenerationBailoutErrorWithDynamicError, throwForSearchParamsAccessInUseCache } from './utils';\nexport function createSearchParamsFromClient(underlyingSearchParams, workStore) {\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-client':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n                return createStaticPrerenderSearchParams(workStore, workUnitStore);\n            case 'prerender-runtime':\n                throw Object.defineProperty(new InvariantError('createSearchParamsFromClient should not be called in a runtime prerender.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E769\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'cache':\n            case 'private-cache':\n            case 'unstable-cache':\n                throw Object.defineProperty(new InvariantError('createSearchParamsFromClient should not be called in cache contexts.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E739\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'request':\n                return createRenderSearchParams(underlyingSearchParams, workStore);\n            default:\n                workUnitStore;\n        }\n    }\n    throwInvariantForMissingStore();\n}\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata = createServerSearchParamsForServerPage;\nexport function createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-client':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n                return createStaticPrerenderSearchParams(workStore, workUnitStore);\n            case 'cache':\n            case 'private-cache':\n            case 'unstable-cache':\n                throw Object.defineProperty(new InvariantError('createServerSearchParamsForServerPage should not be called in cache contexts.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E747\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'prerender-runtime':\n                return createRuntimePrerenderSearchParams(underlyingSearchParams, workUnitStore);\n            case 'request':\n                return createRenderSearchParams(underlyingSearchParams, workStore);\n            default:\n                workUnitStore;\n        }\n    }\n    throwInvariantForMissingStore();\n}\nexport function createPrerenderSearchParamsForClientPage(workStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    }\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-client':\n                // We're prerendering in a mode that aborts (cacheComponents) and should stall\n                // the promise to ensure the RSC side is considered dynamic\n                return makeHangingPromise(workUnitStore.renderSignal, workStore.route, '`searchParams`');\n            case 'prerender-runtime':\n                throw Object.defineProperty(new InvariantError('createPrerenderSearchParamsForClientPage should not be called in a runtime prerender.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E768\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'cache':\n            case 'private-cache':\n            case 'unstable-cache':\n                throw Object.defineProperty(new InvariantError('createPrerenderSearchParamsForClientPage should not be called in cache contexts.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E746\",\n                    enumerable: false,\n                    configurable: true\n                });\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n            case 'request':\n                return Promise.resolve({});\n            default:\n                workUnitStore;\n        }\n    }\n    throwInvariantForMissingStore();\n}\nfunction createStaticPrerenderSearchParams(workStore, prerenderStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    }\n    switch(prerenderStore.type){\n        case 'prerender':\n        case 'prerender-client':\n            // We are in a cacheComponents (PPR or otherwise) prerender\n            return makeHangingSearchParams(workStore, prerenderStore);\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // We are in a legacy static generation and need to interrupt the\n            // prerender when search params are accessed.\n            return makeErroringExoticSearchParams(workStore, prerenderStore);\n        default:\n            return prerenderStore;\n    }\n}\nfunction createRuntimePrerenderSearchParams(underlyingSearchParams, workUnitStore) {\n    return delayUntilRuntimeStage(workUnitStore, process.env.__NEXT_CACHE_COMPONENTS ? makeUntrackedSearchParams(underlyingSearchParams) : makeUntrackedExoticSearchParams(underlyingSearchParams));\n}\nfunction createRenderSearchParams(underlyingSearchParams, workStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    } else {\n        if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n                return makeUntrackedSearchParamsWithDevWarnings(underlyingSearchParams, workStore);\n            }\n            return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams, workStore);\n        } else {\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n                return makeUntrackedSearchParams(underlyingSearchParams);\n            }\n            return makeUntrackedExoticSearchParams(underlyingSearchParams);\n        }\n    }\n}\nconst CachedSearchParams = new WeakMap();\nconst CachedSearchParamsForUseCache = new WeakMap();\nfunction makeHangingSearchParams(workStore, prerenderStore) {\n    const cachedSearchParams = CachedSearchParams.get(prerenderStore);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const promise = makeHangingPromise(prerenderStore.renderSignal, workStore.route, '`searchParams`');\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (Object.hasOwn(promise, prop)) {\n                // The promise has this property directly. we must return it.\n                // We know it isn't a dynamic access because it can only be something\n                // that was previously written to the promise and thus not an underlying searchParam value\n                return ReflectAdapter.get(target, prop, receiver);\n            }\n            switch(prop){\n                case 'then':\n                    {\n                        const expression = '`await searchParams`, `searchParams.then`, or similar';\n                        annotateDynamicAccess(expression, prerenderStore);\n                        return ReflectAdapter.get(target, prop, receiver);\n                    }\n                case 'status':\n                    {\n                        const expression = '`use(searchParams)`, `searchParams.status`, or similar';\n                        annotateDynamicAccess(expression, prerenderStore);\n                        return ReflectAdapter.get(target, prop, receiver);\n                    }\n                default:\n                    {\n                        return ReflectAdapter.get(target, prop, receiver);\n                    }\n            }\n        }\n    });\n    CachedSearchParams.set(prerenderStore, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeErroringExoticSearchParams(workStore, prerenderStore) {\n    const cachedSearchParams = CachedSearchParams.get(workStore);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const underlyingSearchParams = {};\n    // For search params we don't construct a ReactPromise because we want to interrupt\n    // rendering on any property access that was not set from outside and so we only want\n    // to have properties like value and status if React sets them.\n    const promise = Promise.resolve(underlyingSearchParams);\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (Object.hasOwn(promise, prop)) {\n                // The promise has this property directly. we must return it.\n                // We know it isn't a dynamic access because it can only be something\n                // that was previously written to the promise and thus not an underlying searchParam value\n                return ReflectAdapter.get(target, prop, receiver);\n            }\n            switch(prop){\n                case 'then':\n                    {\n                        const expression = '`await searchParams`, `searchParams.then`, or similar';\n                        if (workStore.dynamicShouldError) {\n                            throwWithStaticGenerationBailoutErrorWithDynamicError(workStore.route, expression);\n                        } else if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no cacheComponents)\n                            postponeWithTracking(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            throwToInterruptStaticGeneration(expression, workStore, prerenderStore);\n                        }\n                        return;\n                    }\n                case 'status':\n                    {\n                        const expression = '`use(searchParams)`, `searchParams.status`, or similar';\n                        if (workStore.dynamicShouldError) {\n                            throwWithStaticGenerationBailoutErrorWithDynamicError(workStore.route, expression);\n                        } else if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no cacheComponents)\n                            postponeWithTracking(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            throwToInterruptStaticGeneration(expression, workStore, prerenderStore);\n                        }\n                        return;\n                    }\n                default:\n                    {\n                        if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n                            const expression = describeStringPropertyAccess('searchParams', prop);\n                            if (workStore.dynamicShouldError) {\n                                throwWithStaticGenerationBailoutErrorWithDynamicError(workStore.route, expression);\n                            } else if (prerenderStore.type === 'prerender-ppr') {\n                                // PPR Prerender (no cacheComponents)\n                                postponeWithTracking(workStore.route, expression, prerenderStore.dynamicTracking);\n                            } else {\n                                // Legacy Prerender\n                                throwToInterruptStaticGeneration(expression, workStore, prerenderStore);\n                            }\n                        }\n                        return ReflectAdapter.get(target, prop, receiver);\n                    }\n            }\n        },\n        has (target, prop) {\n            // We don't expect key checking to be used except for testing the existence of\n            // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n            // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n            // you are testing whether the searchParams has a 'then' property.\n            if (typeof prop === 'string') {\n                const expression = describeHasCheckingStringProperty('searchParams', prop);\n                if (workStore.dynamicShouldError) {\n                    throwWithStaticGenerationBailoutErrorWithDynamicError(workStore.route, expression);\n                } else if (prerenderStore.type === 'prerender-ppr') {\n                    // PPR Prerender (no cacheComponents)\n                    postponeWithTracking(workStore.route, expression, prerenderStore.dynamicTracking);\n                } else {\n                    // Legacy Prerender\n                    throwToInterruptStaticGeneration(expression, workStore, prerenderStore);\n                }\n                return false;\n            }\n            return ReflectAdapter.has(target, prop);\n        },\n        ownKeys () {\n            const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n            if (workStore.dynamicShouldError) {\n                throwWithStaticGenerationBailoutErrorWithDynamicError(workStore.route, expression);\n            } else if (prerenderStore.type === 'prerender-ppr') {\n                // PPR Prerender (no cacheComponents)\n                postponeWithTracking(workStore.route, expression, prerenderStore.dynamicTracking);\n            } else {\n                // Legacy Prerender\n                throwToInterruptStaticGeneration(expression, workStore, prerenderStore);\n            }\n        }\n    });\n    CachedSearchParams.set(workStore, proxiedPromise);\n    return proxiedPromise;\n}\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */ export function makeErroringSearchParamsForUseCache(workStore) {\n    const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const promise = Promise.resolve({});\n    const proxiedPromise = new Proxy(promise, {\n        get: function get(target, prop, receiver) {\n            if (Object.hasOwn(promise, prop)) {\n                // The promise has this property directly. we must return it. We know it\n                // isn't a dynamic access because it can only be something that was\n                // previously written to the promise and thus not an underlying\n                // searchParam value\n                return ReflectAdapter.get(target, prop, receiver);\n            }\n            if (typeof prop === 'string' && (prop === 'then' || !wellKnownProperties.has(prop))) {\n                throwForSearchParamsAccessInUseCache(workStore, get);\n            }\n            return ReflectAdapter.get(target, prop, receiver);\n        },\n        has: function has(target, prop) {\n            // We don't expect key checking to be used except for testing the existence of\n            // searchParams so we make all has tests throw an error. this means that `promise.then`\n            // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n            // you are testing whether the searchParams has a 'then' property.\n            if (typeof prop === 'string' && (prop === 'then' || !wellKnownProperties.has(prop))) {\n                throwForSearchParamsAccessInUseCache(workStore, has);\n            }\n            return ReflectAdapter.has(target, prop);\n        },\n        ownKeys: function ownKeys() {\n            throwForSearchParamsAccessInUseCache(workStore, ownKeys);\n        }\n    });\n    CachedSearchParamsForUseCache.set(workStore, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeUntrackedExoticSearchParams(underlyingSearchParams) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingSearchParams);\n    CachedSearchParams.set(underlyingSearchParams, promise);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (!wellKnownProperties.has(prop)) {\n            Object.defineProperty(promise, prop, {\n                get () {\n                    const workUnitStore = workUnitAsyncStorage.getStore();\n                    if (workUnitStore) {\n                        trackDynamicDataInDynamicRender(workUnitStore);\n                    }\n                    return underlyingSearchParams[prop];\n                },\n                set (value) {\n                    Object.defineProperty(promise, prop, {\n                        value,\n                        writable: true,\n                        enumerable: true\n                    });\n                },\n                enumerable: true,\n                configurable: true\n            });\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedSearchParams(underlyingSearchParams) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const promise = Promise.resolve(underlyingSearchParams);\n    CachedSearchParams.set(underlyingSearchParams, promise);\n    return promise;\n}\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams, store) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n    // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n    // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n    // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n    // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n    // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n    let promiseInitialized = false;\n    const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string' && promiseInitialized) {\n                if (store.dynamicShouldError) {\n                    const expression = describeStringPropertyAccess('searchParams', prop);\n                    throwWithStaticGenerationBailoutErrorWithDynamicError(store.route, expression);\n                }\n                const workUnitStore = workUnitAsyncStorage.getStore();\n                if (workUnitStore) {\n                    trackDynamicDataInDynamicRender(workUnitStore);\n                }\n            }\n            return ReflectAdapter.get(target, prop, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (store.dynamicShouldError) {\n                    const expression = describeHasCheckingStringProperty('searchParams', prop);\n                    throwWithStaticGenerationBailoutErrorWithDynamicError(store.route, expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            if (store.dynamicShouldError) {\n                const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n                throwWithStaticGenerationBailoutErrorWithDynamicError(store.route, expression);\n            }\n            return Reflect.ownKeys(target);\n        }\n    });\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = makeDevtoolsIOAwarePromise(underlyingSearchParams);\n    promise.then(()=>{\n        promiseInitialized = true;\n    });\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n            Object.defineProperty(promise, prop, {\n                get () {\n                    return proxiedUnderlying[prop];\n                },\n                set (newValue) {\n                    Object.defineProperty(promise, prop, {\n                        value: newValue,\n                        writable: true,\n                        enumerable: true\n                    });\n                },\n                enumerable: true,\n                configurable: true\n            });\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (prop === 'then' && store.dynamicShouldError) {\n                const expression = '`searchParams.then`';\n                throwWithStaticGenerationBailoutErrorWithDynamicError(store.route, expression);\n            }\n            if (typeof prop === 'string') {\n                if (!wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = describeStringPropertyAccess('searchParams', prop);\n                    syncIODev(store.route, expression);\n                }\n            }\n            return ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return Reflect.set(target, prop, value, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (!wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = describeHasCheckingStringProperty('searchParams', prop);\n                    syncIODev(store.route, expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            const expression = '`Object.keys(searchParams)` or similar';\n            syncIODev(store.route, expression, unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedSearchParams.set(underlyingSearchParams, proxiedPromise);\n    return proxiedPromise;\n}\n// Similar to `makeDynamicallyTrackedExoticSearchParamsWithDevWarnings`, but\n// just logging the sync access without actually defining the search params on\n// the promise.\nfunction makeUntrackedSearchParamsWithDevWarnings(underlyingSearchParams, store) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    const promise = makeDevtoolsIOAwarePromise(underlyingSearchParams);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (!wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = describeStringPropertyAccess('searchParams', prop);\n                    warnForSyncAccess(store.route, expression);\n                }\n            }\n            return ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return Reflect.set(target, prop, value, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (!wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = describeHasCheckingStringProperty('searchParams', prop);\n                    warnForSyncAccess(store.route, expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            const expression = '`Object.keys(searchParams)` or similar';\n            warnForIncompleteEnumeration(store.route, expression, unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedSearchParams.set(underlyingSearchParams, proxiedPromise);\n    return proxiedPromise;\n}\nfunction syncIODev(route, expression, missingProperties) {\n    // In all cases we warn normally\n    if (missingProperties && missingProperties.length > 0) {\n        warnForIncompleteEnumeration(route, expression, missingProperties);\n    } else {\n        warnForSyncAccess(route, expression);\n    }\n    const workUnitStore = workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'request':\n                if (workUnitStore.prerenderPhase === true) {\n                    // When we're rendering dynamically in dev, we need to advance out of\n                    // the Prerender environment when we read Request data synchronously.\n                    trackSynchronousRequestDataAccessInDev(workUnitStore);\n                }\n                break;\n            case 'prerender':\n            case 'prerender-client':\n            case 'prerender-runtime':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n            case 'cache':\n            case 'private-cache':\n            case 'unstable-cache':\n                break;\n            default:\n                workUnitStore;\n        }\n    }\n}\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(createSearchAccessError);\nconst warnForIncompleteEnumeration = createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError);\nfunction createSearchAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`searchParams\\` should be awaited before using its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E249\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction createIncompleteEnumerationError(route, expression, missingProperties) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`searchParams\\` should be awaited before using its properties. ` + `The following properties were not available through enumeration ` + `because they conflict with builtin or well-known property names: ` + `${describeListOfPropertyNames(missingProperties)}. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E2\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction describeListOfPropertyNames(properties) {\n    switch(properties.length){\n        case 0:\n            throw Object.defineProperty(new InvariantError('Expected describeListOfPropertyNames to be called with a non-empty list of strings.'), \"__NEXT_ERROR_CODE\", {\n                value: \"E531\",\n                enumerable: false,\n                configurable: true\n            });\n        case 1:\n            return `\\`${properties[0]}\\``;\n        case 2:\n            return `\\`${properties[0]}\\` and \\`${properties[1]}\\``;\n        default:\n            {\n                let description = '';\n                for(let i = 0; i < properties.length - 1; i++){\n                    description += `\\`${properties[i]}\\`, `;\n                }\n                description += `, and \\`${properties[properties.length - 1]}\\``;\n                return description;\n            }\n    }\n}\n\n//# sourceMappingURL=search-params.js.map", "import {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type StaticPrerenderStoreModern,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n  type PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForRoute should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForServerSegment should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params\n): Promise<Params> {\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError(\n      'Missing workStore in createPrerenderParamsForClientSegment'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams) {\n          for (let key in underlyingParams) {\n            if (fallbackParams.has(key)) {\n              // This params object has one or more fallback params, so we need\n              // to consider the awaiting of this params object \"dynamic\". Since\n              // we are in cacheComponents mode we encode this as a promise that never\n              // resolves.\n              return makeHangingPromise(\n                workUnitStore.renderSignal,\n                workStore.route,\n                '`params`'\n              )\n            }\n          }\n        }\n        break\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderParamsForClientSegment should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'prerender-runtime':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createStaticPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<Params> {\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            // This params object has one or more fallback params, so we need\n            // to consider the awaiting of this params object \"dynamic\". Since\n            // we are in cacheComponents mode we encode this as a promise that never\n            // resolves.\n            return makeHangingParams(\n              underlyingParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            return makeErroringExoticParams(\n              underlyingParams,\n              fallbackParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  if (process.env.__NEXT_CACHE_COMPONENTS) {\n    return makeUntrackedParams(underlyingParams)\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\nfunction createRuntimePrerenderParams(\n  underlyingParams: Params,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<Params> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    process.env.__NEXT_CACHE_COMPONENTS\n      ? makeUntrackedParams(underlyingParams)\n      : makeUntrackedExoticParams(underlyingParams)\n  )\n}\n\nfunction createRenderParamsInProd(underlyingParams: Params): Promise<Params> {\n  if (process.env.__NEXT_CACHE_COMPONENTS) {\n    return makeUntrackedParams(underlyingParams)\n  }\n\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParamsInDev(\n  underlyingParams: Params,\n  devFallbackParams: FallbackRouteParams | null | undefined,\n  workStore: WorkStore\n): Promise<Params> {\n  let hasFallbackParams = false\n  if (devFallbackParams) {\n    for (let key in underlyingParams) {\n      if (devFallbackParams.has(key)) {\n        hasFallbackParams = true\n        break\n      }\n    }\n  }\n  if (process.env.__NEXT_CACHE_COMPONENTS) {\n    return makeDynamicallyTrackedParamsWithDevWarnings(\n      underlyingParams,\n      hasFallbackParams,\n      workStore\n    )\n  }\n\n  return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n    underlyingParams,\n    hasFallbackParams,\n    workStore\n  )\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nconst fallbackParamsProxyHandler: ProxyHandler<Promise<Params>> = {\n  get: function get(target, prop, receiver) {\n    if (prop === 'then' || prop === 'catch' || prop === 'finally') {\n      const originalMethod = ReflectAdapter.get(target, prop, receiver)\n\n      return {\n        [prop]: (...args: unknown[]) => {\n          const store = dynamicAccessAsyncStorage.getStore()\n\n          if (store) {\n            store.abortController.abort(\n              new Error(`Accessed fallback \\`params\\` during prerendering.`)\n            )\n          }\n\n          return new Proxy(\n            originalMethod.apply(target, args),\n            fallbackParamsProxyHandler\n          )\n        },\n      }[prop]\n    }\n\n    return ReflectAdapter.get(target, prop, receiver)\n  },\n}\n\nfunction makeHangingParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = new Proxy(\n    makeHangingPromise<Params>(\n      prerenderStore.renderSignal,\n      workStore.route,\n      '`params`'\n    ),\n    fallbackParamsProxyHandler\n  )\n\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  hasFallbackParams: boolean,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = hasFallbackParams\n    ? makeDevtoolsIOAwarePromise(underlyingParams)\n    : // We don't want to force an environment transition when this params is not part of the fallback params set\n      Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticParamsWithDevWarnings`, but just\n// logging the sync access without actually defining the params on the promise.\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params,\n  hasFallbackParams: boolean,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = hasFallbackParams\n    ? makeDevtoolsIOAwarePromise(underlyingParams)\n    : // We don't want to force an environment transition when this params is not part of the fallback params set\n      Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n", "'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n", "'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when cacheComponents is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n", "'use client'\n\n// This is a client component that only renders during SSR,\n// but will be replaced during streaming with an icon insertion script tag.\n// We don't want it to be presented anywhere so it's only visible during streaming,\n// right after the icon meta tags so that browser can pick it up as soon as it's rendered.\n// Note: we don't just emit the script here because we only need the script if it's not in the head,\n// and we need it to be hoistable alongside the other metadata but sync scripts are not hoistable.\nexport const IconMark = () => {\n  if (typeof window !== 'undefined') {\n    return null\n  }\n  return <meta name=\"«nxt-icon»\" />\n}\n", "'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n", "'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from './boundary-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [ROOT_LAYOUT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n\nexport const RootLayoutBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[\n    ROOT_LAYOUT_BOUNDARY_NAME.slice(0) as typeof ROOT_LAYOUT_BOUNDARY_NAME\n  ]\n"], "names": ["HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status", "BAILOUT_TO_CSR", "BailoutToCSRError", "Error", "constructor", "reason", "isBailoutToCSRError", "err", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "InvariantError", "message", "options", "endsWith", "name", "workAsyncStorage", "workUnitAsyncStorage", "bailoutToClientRendering", "workStore", "getStore", "forceStatic", "workUnitStore", "type", "React", "useContext", "TemplateContext", "RenderFromTemplateContext", "children", "ReflectAdapter", "throwToInterruptStaticGeneration", "postponeWithTracking", "trackSynchronousRequestDataAccessInDev", "delayUntilRuntimeStage", "throwInvariantForMissingStore", "describeStringPropertyAccess", "wellKnownProperties", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "dynamicAccessAsyncStorage", "createParamsFromClient", "underlyingParams", "createStaticPrerenderParams", "process", "env", "NODE_ENV", "devFallbackParams", "createRenderParamsInDev", "createRenderParamsInProd", "createServerParamsForMetadata", "createServerParamsForServerSegment", "createServerParamsForRoute", "createRuntimePrerenderParams", "createPrerenderParamsForClientSegment", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "renderSignal", "route", "Promise", "resolve", "prerenderStore", "makeHangingParams", "makeErroringExoticParams", "__NEXT_CACHE_COMPONENTS", "makeUntrackedParams", "makeUntrackedExoticParams", "hasFallbackParams", "makeDynamicallyTrackedParamsWithDevWarnings", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "fallbackParamsProxyHandler", "get", "target", "prop", "receiver", "originalMethod", "args", "store", "abortController", "abort", "Proxy", "apply", "cachedParams", "promise", "set", "augmentedUnderlying", "keys", "for<PERSON>ach", "defineProperty", "expression", "dynamicTracking", "enumerable", "newValue", "value", "writable", "configurable", "proxiedProperties", "unproxiedProperties", "push", "add", "proxiedPromise", "syncIODev", "delete", "ownKeys", "Reflect", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "prerenderPhase", "length", "createParamsAccessError", "createIncompleteEnumerationError", "describeListOfPropertyNames", "properties", "description", "i", "ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "require", "clientSearchParams", "clientParams", "createSearchParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient", "ClientSegmentRoot", "slots", "IconMark", "meta", "Suspense", "use", "MetadataOutlet", "AsyncMetadataOutlet", "fallback", "NameSpace", "MetadataBoundary", "slice", "ViewportBoundary", "OutletBoundary", "RootLayoutBoundary"], "mappings": "oOAAO,IAAMA,EAAwB,CACnCC,UAAW,IACXC,UAAW,IACXC,aAAc,GAChB,EAAC,AAEKC,EAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACP,IAE/BQ,EAAiC,2BAA0B,AAajE,SAASC,EACdC,CAAc,EAEd,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,CAAA,WAAYA,CAAAA,CAAI,EACM,UAAxB,AACA,OADOA,EAAMC,MAAM,CAEnB,OAAO,EAET,GAAM,CAACC,EAAQC,EAAW,CAAGH,EAAMC,MAAM,CAACG,KAAK,CAAC,KAEhD,OACEF,IAAWJ,GACXJ,EAAcW,GAAG,CAACC,OAAOH,GAE7B,CAEO,SAASI,EACdP,CAA8B,EAG9B,OAAOM,OAAOH,AADKH,EAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE,CAE/C,CAEO,SAASI,EACdC,CAAc,EAEd,OAAQA,GACN,KAAK,IACH,MAAO,cACT,MAAK,IACH,MAAO,WACT,MAAK,IACH,MAAO,WACT,SACE,MACJ,CACF,4EC5DA,IAAA,EAAA,EAAA,CAAA,CAAA,oICAO,IAAI,EAAmC,SAAS,CAAkB,EAIrE,OAJ4B,AAC5B,CAAkB,CAAC,EAAmB,OADC,CACU,CAAG,IAAI,CAAG,EAAtB,SACrC,CAAkB,CAAC,EAAmB,gBAAD,CAAqB,CAAG,IAAI,CAAG,oBACpE,CAAkB,CAAC,EAAmB,gBAAD,CAAqB,CAAG,IAAI,CAAG,oBAC7D,CACX,EAAE,CAAC,GCJI,CDMP,GCNa,EAAsB,gBAC5B,IAAI,EAA6B,SAAS,CAAY,EAGzD,CAHsB,MACtB,EDI4C,ACJ/B,GADoB,CACb,CAAG,KAAX,EACZ,EAAa,OAAU,CAAG,EAAd,QACL,CACX,EAAE,CAAC,GAOQ,SAAS,EAAgB,CAAK,EACrC,GAAqB,UAAjB,OAAO,GAAgC,OAAV,GAAkB,CAAC,CAAC,WAAY,CAAA,CAAK,EAA6B,UAAxB,AAAkC,OAA3B,EAAM,MAAM,CAC1F,MAAO,GAEX,IAAM,EAAS,EAAM,MAAM,CAAC,KAAK,CAAC,KAC5B,CAAC,EAAW,EAAK,CAAG,EACpB,EAAc,EAAO,KAAK,CAAC,EAAG,CAAC,GAAG,IAAI,CAAC,KAEvC,EAAa,OADJ,AACW,EADJ,EAAE,CAAC,CAAC,IAE1B,OAAO,IAAc,IAAwB,AAAS,eAAsB,IAAhC,KAAuB,CAAS,CAAM,EAA4B,UAAvB,OAAO,GAA4B,CAAC,MAAM,IAAe,KAAc,CAClK,CFjBW,CEmBX,QFnBoB,EAAkB,CAAK,EACvC,OAAO,EAAgB,IAAU,CAAA,EAAA,EAAA,WEkBK,cFlBL,AAAyB,EAAC,EAC/D,EAEA,gDAAgD,qCGThD,IAAMC,EAAiB,kCAGhB,OAAMC,UAA0BC,MAGrCC,YAA4BC,CAAc,CAAE,CAC1C,KAAK,CAAE,sCAAqCA,GAAAA,IAAAA,CADlBA,MAAAA,CAAAA,EAAAA,IAAAA,CAFZb,MAAAA,CAASS,CAIzB,CACF,CAGO,SAASK,EAAoBC,CAAY,QAC9C,AAAmB,UAAf,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIf,CAJwD,KAIlD,GAAKS,CACxB,8JCnBO,IAAMO,EAAyB,6BACzBC,AADqD,EAC5B,6BAA4B,AACrDC,EAAuB,2BAA0B,AACjDC,EAA4B,gCAA+B,qDCHjE,OAAMC,UAAuBT,MAClCC,YAAYS,CAAe,CAAEC,CAAsB,CAAE,CACnD,KAAK,CACF,eAAaD,CAAAA,CAAQE,QAAQ,CAAC,KAAOF,EAAUA,EAAU,GAAA,CAAE,CAAE,6BAC9DC,GAEF,IAAI,CAACE,IAAI,CAAG,gBACd,CACF,4CIRO,SAAS,EAA+B,CAAG,QAC9C,AAAmB,UAAf,OAAO,GAA4B,OAAR,CAAgB,CAAC,CAAC,WAAY,GAAG,AAGzD,EAAI,CAHwD,KAGlD,GAAK,CAC1B,mHACA,IAAM,EAA4B,2BAClC,OAAM,UAAqC,MACvC,YAAY,CAAK,CAAE,CAAU,CAAC,CAC1B,KAAK,CAAC,CAAC,qBAAqB,EAAE,EAAW,qGAAqG,EAAE,EAAW,8KAA8K,EAAE,EAAM,EAAE,CAAC,EAAG,IAAI,CAAC,KAAK,CAAG,EAAO,IAAI,CAAC,UAAU,CAAG,EAAY,IAAI,CAAC,MAAM,CAAG,CAC3Z,CACJ,CACA,IAAM,EAAyB,IAAI,QAOxB,SAAS,EAAmB,CAAM,CAAE,CAAK,CAAE,CAAU,EAC5D,GAAI,EAAO,OAAO,CACd,CADgB,MACT,QAAQ,MAAM,CAAC,IAAI,EAA6B,EAAO,GAC3D,EACH,IAAM,EAAiB,IAAI,QAAQ,CAAC,EAAG,KACnC,IAAM,EAAiB,EAAO,IAAI,CAAC,KAAM,IAAI,EAA6B,EAAO,IAC7E,EAAmB,EAAuB,GAAG,CAAC,GAClD,GAAI,EACA,EAAiB,IAAI,CAAC,OACnB,CACH,CAHkB,GAGZ,EAAY,CACd,EACH,CACD,EAAuB,GAAG,CAAC,EAAQ,GACnC,EAAO,gBAAgB,CAAC,QAAS,KAC7B,IAAI,IAAI,EAAI,EAAG,EAAI,EAAU,MAAM,CAAE,IAAI,AACrC,CAAS,CAAC,EAAE,EAEpB,EAAG,CACC,MAAM,CACV,EACJ,CACJ,GAKA,OADA,EAAe,KAAK,CAAC,GACd,CACX,CACJ,CACA,SAAS,IAAgB,CAClB,SAAS,EAA2B,CAAU,EAGjD,OAAO,IAAI,QAAQ,AAAC,IAEhB,WAAW,KACP,EAAQ,EACZ,EAAG,EACP,EACJ,EAEA,mDAAmD,03BHxCnD,IAAA,EAAA,EAAA,CAAA,CAAA,4ECrBA,IAAM,EAAqB,sBACpB,OAAM,UAA2B,MACpC,YAAY,CAAW,CAAC,CACpB,KAAK,CAAC,yBAA2B,GAAc,IAAI,CAAC,WAAW,CAAG,EAAa,IAAI,CAAC,MAAM,CAAG,CACjG,CACJ,CACO,SAAS,EAAqB,CAAG,QACpC,AAAmB,UAAf,OAAO,GAA4B,OAAR,CAAgB,CAAC,CAAC,WAAY,GAA8B,AAA3B,UAAqC,AAAhC,OAAO,EAAI,MAAM,EAG/E,EAAI,MAAM,GAAK,CAC1B,EAEA,yCCZO,ODYyC,ACZnC,UAA8B,MACvC,YAAY,GAAG,CAAI,CAAC,CAChB,KAAK,IAAI,GAAO,IAAI,CAAC,IAAI,CAHD,EAGI,uBAChC,CACJ,CFmBA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OItBW,IAAM,EAAqB,AAAC,IAOnC,QAAQ,OAAO,GAAG,IAAI,CAAC,KAIf,QAAQ,QAAQ,CAAC,EAEzB,EACJ,EJUA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAiD,YAAnC,OAAO,EAAA,OAAK,CAAC,iBAAiB,CAC3C,SAAS,EAA2B,CAAsB,EAC7D,MAAO,wBACH,EACA,gBAAiB,EAAE,CACnB,0BAA2B,IAC/B,CACJ,CACO,SAAS,IACZ,MAAO,CACH,sBAAsB,EACtB,mBAAoB,GACpB,oBAAoB,EACpB,kBAAmB,GACnB,cAAe,EAAE,AACrB,CACJ,CACO,SAAS,EAAsB,CAAa,EAC/C,IAAI,EACJ,OAAO,AAAwE,MAAvE,GAAkC,EAAc,eAAe,CAAC,EAAA,AAAE,EAAY,KAAK,EAAI,EAAgC,UAAU,AAC7I,CAOW,SAAS,EAA0B,CAAK,CAAE,CAAa,CAAE,CAAU,EAC1E,GAAI,EACA,OAAO,EAAc,IAAI,AADV,EAEX,IAAK,QACL,IAAK,iBAML,IAAK,gBADD,MAUR,CAKJ,IAAI,EAAM,YAAY,GAAI,EAAM,WAAW,EAAE,AAC7C,GAAI,EAAM,kBAAkB,CACxB,CAD0B,KACpB,OAAO,cAAc,CAAC,IAAI,EAAsB,CAAC,MAAM,EAAE,EAAM,KAAK,CAAC,8EAA8E,EAAE,EAAW,4HAA4H,CAAC,EAAG,oBAAqB,CACvT,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,gBACD,OAAO,EAAqB,EAAM,KAAK,CAAE,EAAY,EAAc,eAAe,CACtF,KAAK,mBACD,EAAc,UAAU,CAAG,EAG3B,IAAM,EAAM,OAAO,cAAc,CAAC,IAAI,EAAmB,CAAC,MAAM,EAAE,EAAM,KAAK,CAAC,iDAAiD,EAAE,EAAW,2EAA2E,CAAC,EAAG,oBAAqB,CAC5O,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAGA,OAFA,EAAM,uBAAuB,CAAG,EAChC,EAAM,iBAAiB,CAAG,EAAI,KAAK,CAC7B,CAQd,EAER,CAMW,SAAS,EAAiC,CAAU,CAAE,CAAK,CAAE,CAAc,EAElF,IAAM,EAAM,OAAO,cAAc,CAAC,IAAI,EAAmB,CAAC,MAAM,EAAE,EAAM,KAAK,CAAC,mDAAmD,EAAE,EAAW,6EAA6E,CAAC,EAAG,oBAAqB,CAChP,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAIA,OAHA,EAAe,UAAU,CAAG,EAC5B,EAAM,uBAAuB,CAAG,EAChC,EAAM,iBAAiB,CAAG,EAAI,KAAK,CAC7B,CACV,CAOW,SAAS,EAAgC,CAAa,EAC7D,OAAO,EAAc,IAAI,EACrB,IAAK,QACL,IAAK,iBAML,IAAK,gBADD,MAiBR,CACJ,CACA,SAAS,EAAoC,CAAK,CAAE,CAAU,CAAE,CAAc,EAE1E,IAAM,EAAQ,EADC,CAAC,MAAM,EAAE,EAAM,mBACgB,8CADiD,EAAE,EAAW,CAAC,CAAC,EAE9G,EAAe,UAAU,CAAC,KAAK,CAAC,GAChC,IAAM,EAAkB,EAAe,eAAe,CAClD,GACA,EAAgB,YADC,GACc,CAAC,IAAI,CAAC,CAGjC,MAAO,EAAgB,sBAAsB,CAAO,AAAJ,QAAY,KAAK,MAAG,aACpE,CACJ,EAER,CACO,SAAS,EAAmC,CAAK,CAAE,CAAU,CAAE,CAAc,CAAE,CAAc,EAChG,IAAM,EAAkB,EAAe,eAAe,CACtD,EAAoC,EAAO,EAAY,GAKnD,GACkD,MAAM,CAApD,EAAgB,KADH,oBAC4B,EACzC,GAAgB,yBAAyB,CAAG,CAAA,CAGxD,CACO,SAAS,EAAsC,CAAY,EAG9D,EAAa,cAAc,EAAG,CAClC,CAUW,SAAS,EAA4C,CAAK,CAAE,CAAU,CAAE,CAAc,CAAE,CAAc,EAE7G,IAAgC,IAA5B,AADoB,EAAe,UAAU,CAAC,MAAM,CACpC,OAAO,CAAY,CAMnC,EAAoC,EAAO,EAAY,GAKvD,IAAM,EAAkB,EAAe,eAAe,AAClD,IACkD,MAAM,CAApD,EAAgB,IADH,qBAC4B,GACzC,EAAgB,yBAAyB,CAAG,CAAA,CAGxD,CACA,MAAM,EAAgC,CAAC,MAAM,EAAE,EAAM,iEAAiE,EAAE,EAAW,CAAC,CAAC,CACzI,CAOW,SAAS,EAAuB,CAAe,EAClD,EAAgB,yBAAyB,EAAE,AAG3C,QAAQ,KAAK,CAAC,EAAgB,yBAAyB,CAE/D,CAEO,IAAM,EAAyC,EAC/C,SAAS,EAAS,QAAE,CAAM,OAAE,CAAK,CAAE,EACtC,IAAM,EAAiB,EAAA,oBAAoB,CAAC,QAAQ,GAEpD,EAAqB,EAAO,EADJ,GAA0C,GAC9B,eADM,EAAe,IAAI,CAAuB,EAAe,eAAe,CAAG,KAEzH,CACO,SAAS,EAAqB,CAAK,CAAE,CAAU,CAAE,CAAe,EACnE,AA4EJ,SAAS,GACL,GAAI,CAAC,EACD,MAAM,KADQ,EACD,cAAc,CAAC,AAAI,MAAM,CAAC,gIAAgI,CAAC,EAAG,oBAAqB,CAC5L,MAAO,OACP,YAAY,EACZ,aAAc,EAClB,EAER,KAnFQ,GACA,EAAgB,YADC,GACc,CAAC,IAAI,CAAC,CAGjC,MAAO,EAAgB,sBAAsB,CAAG,AAAI,QAAQ,KAAK,MAAG,EACpE,YACJ,GAEJ,EAAA,OAAK,CAAC,iBAAiB,CAAC,EAAqB,EAAO,GACxD,CACA,SAAS,EAAqB,CAAK,CAAE,CAAU,EAC3C,MAAO,CAAC,MAAM,EAAE,EAAM,iEAAiE,EAAE,EAAW,kKAAE,CAAC,AAC3G,CACO,EAFuG,CAAC,MAE/F,EAAkB,CAAG,QACjC,AAAmB,UAAf,OAAO,GAA4B,OAAR,GAAuC,UAAU,AAAjC,OAAO,EAAI,OAAO,EACtD,EAAwB,EAAI,AAJmJ,CAAC,GAAG,CAAC,EAIjJ,CAGlD,CACA,SAAS,EAAwB,CAAM,EACnC,OAAO,EAAO,QAAQ,CAAC,6CATyP,CAAC,sBASpL,EAAO,QAAQ,CAAC,gEACjH,CACA,GAAI,CAAgE,MAAxC,CAA+C,CAA1B,MAAO,QACpD,MAAM,OAAO,cAAc,CAAK,AAAJ,MAAU,0FAA2F,oBAAqB,CAClJ,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,IAAM,EAA6B,6BACnC,SAAS,EAAgC,CAAO,EAC5C,IAAM,EAAQ,OAAO,cAAc,CAAC,AAAI,MAAM,GAAU,oBAAqB,CACzE,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEA,OADA,EAAM,MAAM,CAAG,EACR,CACX,CACO,SAAS,EAA4B,CAAK,EAC7C,MAAwB,UAAjB,OAAO,GAAgC,OAAV,GAAkB,EAAM,MAAM,GAAK,GAA8B,SAAU,GAAS,YAAa,GAAS,aAAiB,KACnK,CACO,SAAS,EAAoB,CAAe,EAC/C,OAAO,EAAgB,MAAM,CAAG,CACpC,CACO,SAAS,EAAqB,CAAa,CAAE,CAAa,EAK7D,OADA,EAAc,eAAe,CAAC,IAAI,IAAI,EAAc,eAAe,EAC5D,EAAc,eAAe,AACxC,CACO,SAAS,EAAyB,CAAe,EACpD,OAAO,EAAgB,MAAM,CAAC,AAAC,GAAiC,UAAxB,OAAO,EAAO,KAAK,EAAiB,EAAO,KAAK,CAAC,MAAM,CAAG,GAAG,GAAG,CAAC,CAAC,YAAE,CAAU,OAAE,CAAK,CAAE,IAC3H,EAAQ,EAAM,KAAK,CAAC,MAAK,AAGxB,KAAK,CAAC,GAAG,MAAM,CAAC,AAAC,KAEV,EAAK,QAAQ,CAAC,uBAAuB,AAIrC,EAAK,QAAQ,CAAC,MAT2E,aASxD,AAIjC,EAAK,QAAQ,CAAC,YAAY,CAI/B,IAAI,CAAC,MACD,CAAC,0BAA0B,EAAE,EAAW;AAAG,EAAE,EAAA,CAAO,EAEnE,CAaW,SAAS,IAChB,IAAM,EAAa,IAAI,gBAMvB,OALA,EAAW,KAAK,CAAC,OAAO,cAAc,CAAC,IAAI,EAAA,iBAAiB,CAAC,qBAAsB,oBAAqB,CACpG,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,IACO,EAAW,MAAM,AAC5B,CAKW,SAAS,EAA8B,CAAa,EAC3D,OAAO,EAAc,IAAI,EACrB,IAAK,YACL,IAAK,oBACD,IAAM,EAAa,IAAI,gBACvB,GAAI,EAAc,WAAW,CAIzB,CAJ2B,CAIb,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,KACxC,EAAW,KAAK,EACpB,OACG,CAaH,IAAM,EAAsB,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,GAC/C,EACA,EAAoB,IAAI,CAAC,IAAI,EAAmB,IAAI,EAAW,AAD1C,KAC+C,KAEpE,EAAmB,IAAI,EAAW,KAAK,GAE/C,CACA,OAAO,EAAW,MAAM,AAC5B,KAAK,mBACL,IAAK,gBACL,IAAK,mBACL,IAAK,UACL,IAAK,QACL,IAAK,gBACL,IAAK,iBACD,MAGR,CACJ,AAJmB,CAKZ,SAAS,EAAsB,CAAU,CAAE,CAAc,EAC5D,IAAM,EAAkB,EAAe,eAAe,CAClD,GACA,EAAgB,YADC,GACc,CAAC,IAAI,CAAC,CACjC,MAAO,EAAgB,sBAAsB,CAAG,AAAI,QAAQ,KAAK,MAAG,aACpE,CACJ,EAER,CACO,SAAS,EAAsB,CAAU,EAC5C,IAAM,EAAY,EAAA,gBAAgB,CAAC,QAAQ,GACrC,EAAgB,EAAA,oBAAoB,CAAC,QAAQ,GACnD,GAAI,GAAa,EACb,OAAO,EAAc,IADO,AACH,EACrB,IAAK,mBACL,IAAK,YACD,CACI,IAAM,EAAiB,EAAc,mBAAmB,AACpD,IAAkB,EAAe,IAAI,CAAG,GAAG,AAI3C,EAAA,OAAK,CAAC,GAAG,CAAC,EAAmB,EAAc,YAAY,CAAE,EAAU,KAAK,CAAE,IAE9E,KACJ,CACJ,IAAK,gBACD,CACI,IAAM,EAAiB,EAAc,mBAAmB,CACxD,GAAI,GAAkB,EAAe,IAAI,CAAG,EACxC,CAD2C,MACpC,EAAqB,EAAU,KAAK,CAAE,EAAY,EAAc,eAAe,EAE1F,KACJ,CACJ,IAAK,oBACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,CAAC,EAAE,EAAE,EAAW,uEAAuE,EAAE,EAAW,+EAA+E,CAAC,EAAG,oBAAqB,CACvP,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,KAAK,QACL,IAAK,gBACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,CAAC,EAAE,EAAE,EAAW,iEAAiE,EAAE,EAAW,+EAA+E,CAAC,EAAG,oBAAqB,CACjP,MAAO,OACP,YAAY,EACZ,aAAc,EAClB,EAOR,CAER,CACA,IAAM,EAAmB,mCAenB,EAA4D,AAAI,OAAO,CAAC,uDAAuD,EAAE,oBAAoB,yCAAyC,+DAAE,EAAA,yBAAyB,CAAC,cAAc,CAAC,EACzO,EAAmB,AAAI,OAAO,CAAC,UAAU,EAAE,EAAA,sBAAsB,CAAC,QAAQ,CAAC,EAC3E,EAAmB,AAAI,OAAO,CAAC,UAAU,EAAE,EAAA,sBAAsB,CAAC,QAAQ,CAAC,EAC3E,EAAiB,AAAI,OAAO,CAAC,UAAU,EAAE,EAAA,oBAAoB,CAAC,QAAQ,CAAC,EACtE,SAAS,EAA0B,CAAS,CAAE,CAAc,CAAE,CAAiB,CAAE,CAAa,EACjG,IAAI,EAAe,IAAI,CAAC,IAGjB,GAAI,EAAiB,IAAI,CAAC,GAHQ,AAGS,CAC9C,EAAkB,kBAAkB,CAAG,GACvC,MACJ,CAAO,GAAI,EAAiB,IAAI,CAAC,GAAiB,CAC9C,EAAkB,kBAAkB,EAAG,EACvC,MACJ,CAAO,GAAI,EAA0D,IAAI,CAAC,GAAiB,CAIvF,EAAkB,iBAAiB,EAAG,EACtC,EAAkB,oBAAoB,EAAG,EACzC,MACJ,MAAO,GAAI,EAAiB,IAAI,CAAC,GAAiB,CAG9C,EAAkB,iBAAiB,EAAG,EACtC,MACJ,KAIO,CAJA,GAAI,EAAc,yBAAyB,CAAE,YAEhD,EAAkB,aAAa,CAAC,IAAI,CAAC,EAAc,yBAAyB,EAI5E,IAAM,EAAQ,AAQlB,SAAS,AAAqC,CAAO,CAAE,CAAc,EAErE,IAAM,EAAQ,OAAO,UAVkC,IAUpB,CAAC,AAAI,MAAM,GAAU,oBAAqB,CACzE,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEA,OADA,EAAM,KAAK,CAAG,EAAM,IAAI,CAAG,KAAO,EAAyB,EACpD,CACX,EAlBwB,CAAC,EAgBuB,CAAC,IAhBjB,CAgB6C,CAhB3C,EAAU,KAAK,CAAC,2NAA2N,CAAC,CAC1M,eAC5D,EAAkB,aAAa,CAAC,IAAI,CAAC,EAEzC,EACJ,CAcO,IAAI,EAA6B,SAAS,CAAY,EAIzD,CAJsB,MACtB,CAAY,CAAC,EAAa,CADO,GACA,CAAG,EAAE,CAAG,EAAhB,KACzB,CAAY,CAAC,EAAa,KAAQ,CAAG,EAAE,CAAG,CAAjB,OACzB,CAAY,CAAC,EAAa,OAAU,CAAG,EAAE,AAAhB,CAAmB,UACrC,CACX,EAAE,CAAC,GACI,SAAS,GAA0B,CAAS,CAAE,CAAK,EACtD,QAAQ,KAAK,CAAC,GACT,EAAU,GAAG,EAAE,CACZ,EAAU,sBAAsB,CAChC,CADkC,OAC1B,KAAK,CAAC,CAAC,iIAAiI,EAAE,EAAU,KAAK,CAAC,2CAA2C,CAAC,EAE9M,QAAQ,KAAK,CAAC,CAAC;0EAC+C,EAAE,EAAU,KAAK,CAAC;qGACS,CAAC,EAGtG,CACO,SAAS,GAAyB,CAAS,CAAE,CAAO,CAAE,CAAiB,CAAE,CAAa,EACzF,GAAgB,IAAZ,EAAe,CACf,GAAI,EAAkB,oBAAoB,CAItC,CAJwC,MAM5C,GAAI,EAAc,yBAAyB,CAKvC,CALyC,KAIzC,GAA0B,EAAW,EAAc,yBAAyB,EACtE,IAAI,EAKd,IAAM,EAAgB,EAAkB,aAAa,CACrD,GAAI,EAAc,MAAM,CAAG,EAAG,CAC1B,IAAI,IAAI,EAAI,EAAG,EAAI,EAAc,MAAM,CAAE,IAAI,AACzC,GAA0B,EAAW,CAAa,CAAC,EAAE,CAEzD,OAAM,IAAI,CACd,CAKA,GAAI,EAAkB,kBAAkB,CAEpC,CAFsC,KACtC,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAE,EAAU,KAAK,CAAC,8QAA8Q,CAAC,EACjT,IAAI,EAEd,GAAgB,GAAG,CAAf,EAKA,MADA,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAE,EAAU,KAAK,CAAC,wGAAwG,CAAC,EAC3I,IAAI,CAElB,MACI,CADG,GACyC,IAAxC,EAAkB,iBAAiB,EAAc,EAAkB,kBAAkB,CAErF,CAFuF,KACvF,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAE,EAAU,KAAK,CAAC,8PAA8P,CAAC,EACjS,IAAI,CAGtB,CACO,SAAS,GAAuB,CAAc,CAAE,CAAM,SACzD,AAAI,EAAe,mBAAmB,CAC3B,CAD6B,CACd,mBAAmB,CAAC,IAAI,CAAC,IAAI,GAEhD,CACX,EAEA,6CAA6C,OMvkBtC,SAAS,EAAiB,CAAK,EAClC,GAAI,CAAA,EAAA,EAAA,iBAAA,AAAiB,EAAC,IAAU,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,IAAU,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,IAAU,CAAA,EAAA,EAAA,iBAAA,AAAiB,EAAC,IDLvF,MCKiG,IDLlH,OAAO,GAAsB,AAAU,OCKsF,GDL9E,EAAM,QAAQ,GAAK,GCKqE,CAAA,EAAA,EAAA,8BAAA,AAA8B,EAAC,GACzK,KADiL,CAC3K,EAEN,aAAiB,OAAS,UAAW,GACrC,EAAiB,EAAM,AADqB,KAChB,CAEpC,EAEA,QAfA,IAAA,EAAA,EAAA,CAAA,CAAA,ODAA,IAAM,EAAsB,OAAO,GAAG,CAAC,SCeY,SAbnD,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,uECLA,IAAA,EAAkC,EAAA,CAAA,AAAzBd,CAAyB,OAClC,EAAiC,EAAA,CAD+C,AACvEe,AAAwB,CAAA,GADP,IAE1B,EAAqC,EAFH,AAEG,CAA5BC,AAA4B,CAAA,EADZ,GAA6D,EAG/E,GAH0B,MAGjBC,EAAyBd,CAFZ,AAE0B,EACrD,IAAMe,EAAYH,AAHiB,AAA0D,EAG3EA,gBAAAA,CAAiBI,QAAQ,GAE3C,GAAID,MAAAA,EAAAA,KAAAA,EAAAA,EAAWE,WAAW,CAAE,OAE5B,IAAMC,EAAgBL,EAAAA,oBAAAA,CAAqBG,QAAQ,GAEnD,GAAIE,EACF,OAAQA,EAAcC,IADL,AACS,EACxB,IAAK,YACL,IAAK,oBACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACH,MAAM,OAAA,cAA6B,CAA7B,IAAItB,EAAAA,iBAAAA,CAAkBG,GAAtB,oBAAA,OAAA,mBAAA,gBAAA,CAA4B,EAQtC,CAEJ,+EG5BA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OA0CW,SAAS,UAKhB,CAzCA,AAyCI,SAzCK,EAC0B,CAE/B,GAAM,WAsCoB,WAtClB,CAAoB,CAAE,CAAA,EAAA,CAAA,CAAA,OACxB,EAAgB,EAAqB,QAAQ,GACnD,GAAI,CAAC,EAAe,OAAO,EAC3B,OAAO,EAAc,IAAI,EACrB,IAAK,YACL,IAAK,mBACL,IAAK,gBACD,IAAM,EAAiB,EAAc,mBAAmB,CACxD,QAAO,GAAiB,EAAe,IAAI,CAAG,CAUtD,CACA,EAX0D,KAWnD,CACX,CAEJ,IAsBW,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EAAA,eAAe,EALtB,IAMf,EAEA,gDAAgD,YDxDhD,IAAA,EAAA,EAAA,CAAA,CAAA,WAYA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,KACA,OAAM,UAAwC,EAAA,OAAK,CAAC,SAAS,CACzD,mBAAoB,CAQpB,CACA,OAAO,yBAAyB,CAAK,CAAE,CACnC,GAAI,CAAA,EAAA,EAAA,yBAAyB,AAAzB,EAA0B,GAE1B,KAFkC,CAE3B,CACH,gBAFe,CAAA,AAEE,EAFF,EAAA,2BAAA,AAA2B,EAAC,EAG/C,CAGJ,OAAM,CACV,CACA,OAAO,yBAAyB,CAAK,CAAE,CAAK,CAAE,QAM1C,AAAI,EAAM,QAAQ,GAAK,EAAM,gBAAgB,EAAI,EAAM,eAAe,CAC3D,CACH,AAFgE,qBAE/C,EACjB,iBAAkB,EAAM,QAAQ,AACpC,EAEG,CACH,gBAAiB,EAAM,eAAe,CACtC,iBAAkB,EAAM,QAAQ,AACpC,CACJ,CACA,QAAS,CACL,GAAM,UAAE,CAAQ,WAAE,CAAS,cAAE,CAAY,UAAE,CAAQ,CAAE,CAAG,IAAI,CAAC,KAAK,CAC5D,CAAE,iBAAe,CAAE,CAAG,IAAI,CAAC,KAAK,CAChC,EAAkB,CACpB,CAAC,EAAA,qBAAqB,CAAC,SAAS,CAAC,CAAE,EACnC,CAAC,EAAA,qBAAqB,CAAC,SAAS,CAAC,CAAE,EACnC,CAAC,EAAA,qBAAqB,CAAC,YAAY,CAAC,CAAE,CAC1C,EACA,GAAI,EAAiB,CACjB,IAAM,EAAa,IAAoB,EAAA,qBAAqB,CAAC,SAAS,EAAI,EACpE,EAAc,IAAoB,EAAA,qBAAqB,CAAC,SAAS,EAAI,EACrE,EAAiB,IAAoB,EAAA,qBAAqB,CAAC,YAAY,EAAI,SAEjF,AAAM,GAAc,CAAhB,CAAC,CAA8B,EAGd,CAAA,EAAA,EAAA,IAAA,AAAK,EAAC,CAHsB,CAGtB,EAHyB,MAGhB,CAAE,CAClC,SAAU,CACQ,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,OAAQ,CACvB,KAAM,SACN,QAAS,SACb,IACA,EAIA,CAAe,CAAC,EAAgB,CACnC,AACL,GAdW,CAef,CACA,OAAO,CACX,CACA,YAAY,CAAK,CAAC,CACd,KAAK,CAAC,GACN,IAAI,CAAC,CAZgC,IAY3B,CAAG,CACT,WAbkD,UAajC,CAb4C,CAc7D,EAdgE,IAAA,WAc9C,EAAM,QAAQ,AACpC,CACJ,CACJ,CACO,SAAS,EAA2B,CAAK,EAC5C,GAAI,CAAE,GAnBsE,EAAC,KAmB/D,GAnBuE,QAmBrE,CAAS,cAAE,CAAY,UAAE,CAAQ,CAAE,CAAG,EAKhD,EAAW,IACX,EAAe,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EAAA,kBAAkB,SACtB,AAC5B,GADwC,CACpC,EADiD,EAE5B,CAAA,EAAA,EAAA,GAAA,AAAI,EAFoC,AAEnC,EAAiC,CACvD,CAFc,QAEJ,EACV,SAAU,EACV,UAAW,EACX,aAAc,EACd,aAAc,EACd,SAAU,CACd,GAEiB,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAA,QAAS,CAAE,CACjC,SAAU,CACd,EACJ,EAEA,0CAA0C,SSpH1C,IAAA,EAAA,EAAA,CAAA,CAAA,OOWe,EAA6B,SAAS,CAAY,EAI7D,CAJ0B,MAC1B,EAAa,GADwB,CACjB,CAAG,KAAX,EACZ,EAAa,IAAO,CAAG,KAAX,EACZ,EAAa,SAAY,CAAb,AAAgB,YACrB,CACX,EAAE,CAAC,GPdH,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MFFA,EAAA,EAAA,CAAA,CAAA,OOEO,IAAM,EAAgC,yBAChC,EAA8B,uBAQ9B,EAAW,WASX,EAAuB,OgBjB7B,SAAS,EAAwB,CAAM,EAEtC,MAAM,OAAO,cAAc,CAAC,AAAI,MAAM,2EAA4E,oBAAqB,CACnI,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAGR,CKZO,eAAe,EAAW,CAAQ,CAAE,CAAU,EACjD,OAAO,IAAI,QAAQ,CAAC,EAAS,KACzB,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,KACZ,EAAwB,CACpB,KnBDoB,CmBCd,wBACN,aACA,UACA,SACA,CACJ,EACJ,EACJ,EACJ,C/BbO,C+BeP,G/Bfa,OAiBT,EeJS,EAAmB,OfMhC,CAnBgC,G8BgGzB,SAAS,EAA4B,CAAG,EAC3C,IClFuC,ADkFjC,EAA6B,IAAI,IAAI,O9BjG0B,G8BkGrE,EAA2B,O9B/EoB,K8B+ER,CAAC,MAAM,CAAC,GASxC,CACX,C3BlGA,IAAM,EAA2B,EAAA,wBAA+B,CAChE,SAAS,EAAgB,CAAG,EACxB,MAAO,CACH,WAAY,EAA4B,IAAI,IAAI,EAAK,SAAS,MAAM,GAAG,QAAQ,GAC/E,kBAAc,EACd,oBAAoB,EACpB,aAAa,EACb,WAAW,EACX,UAAW,CAAC,CAChB,CACJ,CACA,IAAI,EAAkB,IAAI,gBAkBf,eAAe,EAAoB,CAAG,CAAE,CAAO,MAuB9C,MAtBR,GAAM,mBAAE,CAAiB,SAAE,CAAO,cAAE,CAAY,CAAE,CAAG,EAC/C,EAAU,CO3CM,IP6CJ,IAEd,CAAC,EAA8B,C+BInC,A/BJ2F,CAAtD,CAA8D,E+BI/F,U/BJ2G,C+BKpG,GADO,gBACY,KAAK,SAAS,CAAC,IAEtC,mBAAmB,KAAK,SAAS,CAAC,AAKzC,SAAS,EAAyC,CAAiB,UACnE,GAAM,CAAC,EAAS,EAAgB,EAAM,EAAe,EAAc,EAAmB,CAAG,EANP,AAS5E,EAyBN,AAAuB,UAAnB,KAzBmB,EAyBZ,AAD+B,EAxBc,IAyBrB,CADc,CACN,UAAU,CAAC,EAAmB,KAC9D,CADoE,CAGxE,EA1BD,EAAwB,CAAC,EAC/B,IAAK,GAAM,CAAC,EAAK,EAAW,GAAI,OAAO,OAAO,CAAC,GAC3C,CAAqB,CAAC,EAAI,CAAG,EAAyC,GAE1E,GAH+D,CAGzD,EAAS,CACX,EACA,EACA,KA0BW,AAzBX,CAwBiC,EAxBL,IAyBkB,AAAlB,OADkB,OAxBD,EAAgB,KAChE,CAQD,YANqB,IAAjB,IACA,CAAM,CAAC,CADqB,CACnB,CAAG,CAAA,OAEW,IAAvB,IACA,CAAM,CAAC,CAD2B,CACzB,CAAG,CAAA,EAET,CACX,E/BpC4E,IACxE,EAMM,IAAiB,EAAa,IAAI,EAAE,CACtC,CAAO,CAAC,EAA4B,CAAG,GAAA,EAKvC,IACA,CAAO,CAAC,EAAS,CADR,AACW,CAAA,EAExB,GAAI,CAMA,IAAM,EAAgB,EAAe,IAAiB,EAAa,SAAS,CAAG,OAAS,MAAQ,OAc1F,EAAM,MAAM,EAAY,EAAK,EAAS,EAAe,EAAgB,MAAM,EAC3E,EAAc,EAA4B,IAAI,IAAI,EAAI,GAAG,GACzD,EAAe,EAAI,UAAU,CAAG,OAAc,EAC9C,EAAc,EAAI,OAAO,CAAC,GAAG,CAAC,iBAAmB,GACjD,EAAe,CAAC,CAAC,CAAC,AAAgD,OAA/C,EAAmB,EAAI,OAAO,CAAC,GAAG,CAAC,OAAA,CAAO,CAAY,KAAK,EAAI,EAAiB,QAAQ,CAAC,EAAA,CAAS,CACrH,EAAY,CAAC,CAAC,EAAI,OAAO,CAAC,GAAG,CAAC,AO/DJ,sBPgE1B,EAAyB,EAAI,OAAO,CAAC,GAAG,CAAC,AOjEV,uBPkE/B,EAAuC,OAA3B,EAAyE,IAAvC,SAAS,EAAwB,IAAa,CAAC,EAWnG,GAAI,CAVmB,AAUlB,EAV8B,UAAU,CAAC,AO5Ef,qBPsFN,CAAC,EAAI,EAAE,EAAI,CAAC,EAAI,IAAI,CAKzC,CAL2C,MAEvC,EAAI,IAAI,EAAE,CACV,EAAY,IAAI,CAAG,EAAI,IAAA,AAAI,EAExB,EAAgB,EAAY,QAAQ,IAU/C,IAAM,EAAe,EAAY,AA+HzC,SAAS,AAA8B,CAAoB,EAYvD,IAAM,EAAS,EAAqB,SAAS,GAC7C,OAAO,IAAI,eAAe,CACtB,MAAM,KAAM,CAAU,EAClB,MAAM,CAAK,CACP,GAAM,MAAE,CAAI,OAAE,CAAK,CAAE,CAAG,MAAM,EAAO,IAAI,GACzC,GAAI,CAAC,EAAM,CAGP,EAAW,OAAO,CAAC,GACnB,QACJ,CAGA,MACJ,CACJ,CACJ,EACJ,EAEA,AA9JuE,EAAI,IAAI,EAAI,EAAI,IAAI,CAC7E,EAAW,MAAM,CAwHc,EAxHe,EAyHjD,EAAyB,EAAc,CAC1C,GAFiD,QAEjD,EACA,GAkCyC,cAlCzC,CACJ,IA3HI,GF1GY,AE0GR,KAAoB,EAAS,CAAC,CAC9B,CADgC,MACzB,EAAgB,EAAI,GAAG,EAElC,MAAO,CACH,UAAA,E+BxFwB,A/BwFZ,EAAoB,EAAS,CAAC,C+BrFlD,AAA0B,IAHgB,MAGtC,AAAgC,OAAzB,EACA,EAEJ,EAAW,GAAG,CAAC,AAAC,GAAiB,CAnCrC,SAAS,AAA2B,CAAc,MAOjD,EAHJ,GAAM,CAAC,EAAM,EAAU,EAAM,EAAc,CAAG,EAAe,KAAK,CAAC,CAAC,GAE9D,EAAc,EAAe,KAAK,CAAC,EAAG,CAAC,GAE7C,MAAO,CAIH,cAAe,EAAY,KAAK,CAAC,EAAG,CAAC,eACrC,EAGA,QAAS,AAAyD,OAAxD,EAAgB,CAAW,CAAC,EAAY,MAAM,CAAG,EAAA,AAAE,EAAY,EAAgB,QACzF,EACA,gBACA,gBACA,EACA,aAnByB,IAmBX,EAAe,MAAM,AACvC,CACJ,GAYuE,CAdvB,I/BiGpC,aAAc,EACd,mBAAoB,EACpB,YAAa,EAAS,CAAC,WACvB,YACA,CACJ,CACJ,CAAE,MAAO,EAAK,CAOV,OANI,AAAC,EAAgB,MAAM,CAAC,OAAO,EAAE,AACjC,QAAQ,KAAK,CAAC,mCAAqC,EAAM,wCAAyC,GAK/F,CACH,WAAY,EAAI,QAAQ,GACxB,kBAAc,EACd,oBAAoB,EACpB,aAAa,EACb,WAAW,EACX,UAAW,CAAC,CAChB,CACJ,CACJ,CACO,eAAe,EAAY,CAAG,CAAE,CAAO,CAAE,CAAa,CAAE,CAAM,EAoBjE,IAAI,EAAW,IAAI,IAAI,EDlJvB,MmBnB2C,QnBoCQ,ECkIxB,EDlI6B,EmBpCC,GnBkBH,CAAO,CAAC,EAA4B,CmBlB/B,EnBkBiC,CAAO,CQRpD,ARQqD,GACjE,KAAK,SmBnBwC,cnBkBwD,CmBlBtD,EnBkBwD,CAAO,CAAC,EAA8B,CmBlB7E,ElBsK9D,ADpJ6I,CAAO,CAAC,EAAS,CmBjBnM,AAAI,CAD6F,AAC5F,KAD2G,AACxF,OAAgC,MAAnB,CAAmB,CAAG,OAA+B,IAA1B,QAA2D,IAApB,QAAmD,IAAlB,EAC7H,GgBaJ,CATJ,ChBLkK,OgBcrJ,CATK,AAAT,CAAY,EACxB,IAAI,EAAO,KACX,IAAI,IAAI,EAAI,EAAG,EAAI,EAAI,MAAM,CAAE,IAAI,AAE/B,EAAO,CAAC,IAAQ,CAAC,CAAI,EADR,EAAI,GACW,OADD,CAAC,GACO,EAEvC,OAAO,IAAS,EACpB,EhBTmB,CACX,GAAkB,IAClB,GAAyB,IACzB,GAAmB,IACnB,GAAiB,IACpB,CAAC,IAAI,CAAC,MgBMc,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAG,InCgCzC,IAAM,EAAiB,EAAI,MAAM,CAI7B,EAAQ,CAHG,EAAe,UAAU,CAAC,KAAO,EAAe,KAAK,CAAC,GAAK,CAAA,EAGrD,KAAK,CAAC,KAAK,MAAM,CAAE,AAAD,GAAQ,GAAQ,CAAC,EAAK,UAAU,CAAC,GAAK,EAAuB,MAClG,EAAK,MAAM,CAAG,EACd,CADiB,CACX,IAAI,CAAC,EAAuB,IAAM,GAExC,EAAM,IAAI,CAAC,GAAK,GAEpB,EAAI,MAAM,CAAG,EAAM,MAAM,CAAG,IAAM,EAAM,IAAI,CAAC,KAAO,EAtCZ,CCoJxC,IAAI,EAAkB,MAAM,MAAM,EAZb,CAEjB,OAUwC,KAV3B,cACb,UACA,SAAU,QAAiB,SAC3B,CACJ,GA4BI,EAAa,EAAgB,UAAU,CA4BrC,EAAc,IAAI,IAAI,EAAgB,GAAG,CAAE,GAiBjD,OAhBA,AAgBO,EAhBK,YAAY,CAAC,MAAM,CAAC,GACZ,CAChB,IAAK,EAAY,IAAI,YAKrB,EAIA,GAAI,EAAgB,EAAE,CACtB,QAAS,EAAgB,OAAO,CAChC,KAAM,EAAgB,IAAI,CAC1B,OAAQ,EAAgB,MAAM,AAClC,CAEJ,C0B1OW,IAAM,EAAqB,CAClC,KAAM,KAAK,CACf,GAEA,OvBHA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OFJA,IAAM,EAAmD,EAAA,CAAA,CAAA,IyBMV,GzBNyE,MAA/F,UAA+G,CAIjI,EAJoI,OAI3H,EAAe,CAAK,EAChC,GAAI,OAAE,CAAK,CAAE,CAAG,EAChB,GAAI,EAAkB,CAClB,IAAM,EAAQ,EAAiB,QAAQ,GACvC,GAAI,CAAU,MAAT,EAAgB,KAAK,EAAI,EAAM,YAAA,AAAY,IAAe,CAAV,KAAC,EAAgB,KAAK,EAAI,EAAM,kBAAA,AAAkB,EAEnG,CAFsG,KACtG,QAAQ,KAAK,CAAC,GACR,CAEd,CACA,OAAO,IACX,C6BVsC,AdEO,CfU7C,4CAA4C,yQeVwB,CcApE,KdA0E,AbGnE,OAAM,UAA6B,EAAA,OAAK,CAAC,K2BHX,I3BGoB,CACrD,OAAO,yBAAyB,CAAK,CAAE,CACnC,GAAI,CAAA,EAAA,EAAA,iBAAA,AAAiB,EAAC,GAGlB,KAH0B,CAGpB,EAEV,MAAO,OACH,CACJ,CACJ,CACA,OAAO,yBAAyB,CAAK,CAAE,CAAK,CAAE,CAC1C,GAAM,CAAE,OAAK,CAAE,CAAG,SAmBlB,AAAI,EAAM,QAAQ,GAAK,EAAM,gBAAgB,EAAI,EAAM,KAAK,CACjD,CACH,AAFsD,MAE/C,KACP,iBAAkB,EAAM,QAAQ,AACpC,EAEG,CACH,MAAO,EAAM,KAAK,CAClB,iBAAkB,EAAM,QAAQ,AACpC,CACJ,CAEA,QAAS,QAGL,AAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAI,CAAC,CACA,CAAA,EAAA,EAAA,IAAA,AAAK,EAAC,EAAA,EADU,MACD,CAAE,CAClC,SAAU,CACQ,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAgB,CAC/B,MAAO,IAAI,CAAC,KAAK,CAAC,KAAK,AAC3B,GACA,IAAI,CAAC,KAAK,CAAC,WAAW,CACtB,IAAI,CAAC,KAAK,CAAC,YAAY,CACT,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAE,CAC1C,MAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACvB,MAAO,IAAI,CAAC,KAAK,AACrB,GAER,AADK,GAGF,IAAI,CAAC,KAAK,CAAC,QACtB,AAD8B,CAE9B,YAAY,CAAK,CAAC,CACd,KAAK,CAAC,GAAQ,IAAI,CAAC,KAAK,CAAG,KACvB,IAAI,CAAC,QAAQ,CAAC,CACV,MAAO,IACX,EACJ,EACA,IAAI,CAAC,KAAK,CAAG,CACT,MAAO,KACP,iBAAkB,IAAI,CAAC,KAAK,CAAC,QAAQ,AACzC,CACJ,CACJ,CAOW,SAAS,EAAc,CAAK,EACnC,GAAI,gBAAE,CAAc,aAAE,CAAW,cAAE,CAAY,UAAE,CAAQ,CAAE,CAAG,EAKxD,EAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,WACrC,AAAI,EACqB,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAsB,CAC5C,CAFY,QAEF,EACV,eAAgB,EAChB,YAAa,EACb,aAAc,EACd,SAAU,CACd,GAEiB,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAA,QAAS,CAAE,CACjC,SAAU,CACd,EACJ,CK7GO,CL+GP,GK/Ga,EAAe,CAAC,EAAiB,IAE1C,AAA+B,UAA3B,AAAqC,OAA9B,EACP,AAAuB,UAAnB,AAA6B,CL4GC,MK5GvB,GAEA,IAAoB,EAInC,AAAuB,UAAU,AAA7B,OAAO,GAGJ,CAAe,CAAC,EAAE,GAAK,CAAO,CAAC,EAAE,EAAI,CAAe,CAAC,EAAE,GAAK,CAAO,CAAC,EAAE,CJZjF,EAAA,CAAA,CAAA,ckBEA,EAAA,CAAA,CAAA,OjBFA,EAAA,CAAA,CAAA,MACA,IAAA,EAAA,EAAA,CAAA,CAAA,OAC2D,EAAA,CAAA,CAAA,OAAiE,kBAAkB,CCF9I,EDEiJ,ECFjJ,EAAA,EAAA,CAAA,CAAA,OAcwB,EAAA,8BAA8B,CQD9B,ERCiC,AQDjC,8BAA8B,CCC9B,EDDiC,ACCjC,8BAA8B,CkBRc,ElBQX,AkBRW,CAAA,CAAA,OAAqC,gBAAgB,CXuFlH,EWvFqH,OXuF5G,IACZ,IAAM,EAAS,CAAA,EAAA,EAAA,GWtFnB,OXsFmB,AAAU,EAAC,EAAA,gBAAgB,EAC1C,GAAe,MAAM,CAAjB,EACA,GWxFoC,GXwF9B,OAAO,cAAc,CAAC,AAAI,MAAM,+CAAgD,oBAAqB,CACvG,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,OAAO,CACX,C3BjGA,SAAS,EAAe,CAAK,EACzB,GAAI,UAAE,CAAQ,OAAE,CAAK,cAAE,CAAY,CAAE,CAAG,EAClC,EAAS,IAgBf,MAfA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACN,EAAA,OAAK,CAAC,eAAe,CAAC,KACd,IAAiB,EAAA,YAAY,CAAC,IAAI,CAClC,CADoC,CAC7B,IAAI,CAAC,EAAU,CAAC,GAEvB,EAAO,OAAO,CAAC,EAAU,CAAC,GAE9B,GACJ,EACJ,EAAG,CACC,EACA,EACA,EACA,EACH,EACM,IACX,CCpBsC,gB0BqEtC,EAAA,CAAA,CAAA,OApE8D,EAAA,CAAA,CAAA,OAAqD,qBAAqB,A3BoBjI,G2BpBoI,I3BoB9H,UAA8B,EAAA,OAAK,CAAC,SAAS,CACtD,OAAO,yBAAyB,CAAK,CAAE,CACnC,GAAI,CAAA,EAAA,EAAA,eAAe,AAAf,EAAgB,GAGhB,KAHwB,CAGjB,CACH,SUYZ,AAAK,CVZiB,AUYjB,EAAA,CAAD,CAAC,eAAA,AAAe,EAAC,GAGd,AVlBqC,EUkB/B,GAHgB,GAGV,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,EAAG,CAAC,GAAG,IAAI,CAAC,KAHb,KVXxB,aUgBT,AVnB0B,SUmBjB,AAAyB,CAAK,EAC1C,GAAI,CAAC,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,GACjB,KADyB,CACnB,OAAO,cAAc,CAAK,AAAJ,MAAU,wBAAyB,oBAAqB,CAChF,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,OAAO,EAAM,MAAM,CAAC,KAAK,CAAC,IAAK,EAAE,CAAC,EACtC,AADwC,EV3BkB,EAI9C,CAGJ,OAAM,CACV,CAEA,QAAS,CACL,GAAM,UAAE,CAAQ,cAAE,CAAY,CAAE,CAAG,IAAI,CAAC,KAAK,QAC7C,AAAiB,OAAb,GAAsC,MAAM,CAAvB,EACA,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAgB,CACtC,SAAU,EACV,aAAc,EACd,MAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CACjB,SAAU,IACd,EACR,GAEG,IAAI,CAAC,KAAK,CAAC,QAAQ,AAC9B,CACA,YAAY,CAAK,CAAC,CACd,KAAK,CAAC,GACN,IAAI,CAAC,KAAK,CAAG,CACT,SAAU,KACV,aAAc,IAClB,CACJ,CACJ,CACO,SAAS,EAAiB,CAAK,EAClC,GAAI,UAAE,CAAQ,CAAE,CAAG,EACb,EAAS,IACf,MAAqB,CAAd,AAAc,EAAA,EAAA,GAAA,AAAI,EAAC,EAAuB,AAA/B,CACd,OAAQ,EACR,SAAU,CACd,EACJ,COxDA,CP0DA,GO1DA,EAAA,EAAA,CAAA,CAAA,OkBXO,SAAS,EAAqB,CAAO,CAAE,CAAuB,QAIjE,CAHgC,KAAK,CzBoEI,GyBpErC,IAAoC,GAA0B,CAAA,EAG9D,MAAM,OAAO,CAAC,IACP,CAAO,CAAC,EAAE,CAAG,CADI,GACE,CAAO,CAAC,EAAE,CAAG,IAAM,CAAO,CAAC,EAAE,CAIvD,GAA2B,EAAQ,UAAU,CAAC,GACvC,EAEJ,CACX,CKZO,CLcP,GKda,EAA6B,CACtC,KLOqE,MKNrE,MACA,OACA,QACH,CvBgDoE,EAAA,OAAQ,CAAC,EkBvC3B,0DlBuCuF,CAY1I,IAAM,EAAiB,CACnB,SACA,SACA,OACA,QACA,MACA,QACA,IACA,IACH,CAuBG,SAAS,EAAuB,CAAO,CAAE,CAAc,EACvD,IAAM,EAAO,EAAQ,qBAAqB,GAC1C,OAAO,EAAK,GAAG,EAAI,GAAK,EAAK,GAAG,EAAI,CACxC,CAgBA,MAAM,UAAmC,EAAA,OAAK,CAAC,SAAS,CACpD,mBAAoB,CAChB,IAAI,CAAC,qBAAqB,EAC9B,CACA,oBAAqB,CAEb,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,AACpC,IAAI,CAAC,qBAAqB,EAElC,CACA,QAAS,CACL,OAAO,IAAI,CAAC,KAAK,CAAC,QACtB,AAD8B,CAE9B,YAAY,GAAG,CAAI,CAAC,CAChB,KAAK,IAAI,GAAO,IAAI,CAAC,qBAAqB,CAAG,KAEzC,GAAM,mBAAE,CAAiB,aAAE,CAAW,CAAE,CAAG,IAAI,CAAC,KAAK,CACrD,GAAI,EAAkB,KAAK,CAAE,CAIzB,GAA8C,IAA1C,EAAkB,YAAY,CAAC,MAAM,EAAU,CAAC,EAAkB,YAAY,CAAC,IAAI,CAAC,AAAC,GAAuB,EAAY,KAAK,CAAC,CAAC,EAAS,IAAQ,EAAa,EAAS,CAAoB,CAAC,EAAM,IACjM,CADsM,MAG1M,IAAI,EAAU,KACR,EAAe,EAAkB,YAAY,CAUnD,GATI,IACA,EArChB,AAqC0B,QADI,CApCrB,AAAuB,CAAY,MAKxC,QAHA,AAAJ,AAAqB,OAAO,GACjB,SAAS,IAAI,CAIjB,AAAsE,OAArE,EAA2B,SAAS,cAAc,CAAC,EAAA,CAAa,CAAY,EACpF,SAAS,iBAAiB,CAAC,EAAa,CAAC,EAAE,AAC/C,EA4BqD,EAAA,EAIjC,AAAC,IACD,EAzF0B,GAwFhB,CACgB,EAG1B,CAAC,CAHS,AAGR,YAHoB,CAGD,OAAA,CAAO,CAC5B,EAD+B,KAKnC,KAAM,CAAC,CAAC,aAAmB,WAAA,CAAW,EAAK,AA/EvD,SAAS,AAAkB,CAAO,EAIlC,GAAI,CACA,SACA,QACH,CAAC,QAAQ,CAAC,iBAAiB,GAAS,QAAQ,EAIzC,CAJ4C,MAIrC,EAIX,IAAM,EAAO,EAAQ,qBAAqB,GAC1C,OAAO,EAAe,KAAK,CAAC,AAAC,GAAsB,IAAf,CAAI,CAAC,EAAK,CAClD,EA8D6E,IAAS,CAUlE,GAAmC,MAAM,CAArC,EAAQ,kBAAkB,CAC1B,OAEJ,EAAU,EAAQ,kBAAkB,AACxC,CAEA,EAAkB,KAAK,EAAG,EAC1B,EAAkB,YAAY,CAAG,KACjC,EAAkB,YAAY,CAAG,EAAE,CE5KxC,AF6KK,SE7K6C,AAAzC,CAA2C,CAAE,CAAO,EAIpE,GAHI,AAAY,KAAK,QAAG,EAAU,EAAC,EAG/B,EAAQ,cAAc,CAAE,OACxB,IAGJ,IAAM,EAAc,SAAS,eAAe,CACnB,EAAY,OAAO,CAAC,cAAc,CAkB3D,IAlBgE,AAkB1D,EAAW,EAAY,KAAK,CAAC,cAAc,CACjD,EAAY,KAAK,CAAC,cAAc,CAAG,OAC9B,AAAD,EAAS,eAAe,EAAE,AAI1B,EAAY,cAAc,GAE9B,IACA,EAAY,KAAK,CAAC,cAAc,CAAG,CACvC,EAEA,AFsIyD,KAErC,GAAI,EAAc,YAEd,EAAQ,cAAc,GAK1B,IAAM,EAAc,EE/IS,OF+IA,eAAe,CACtC,EAAiB,EAAY,YAAY,EAE3C,EAAuB,EAAS,KAOpC,EAAY,SAAS,CAPgC,AAO7B,EAEpB,AAAC,EAAuB,EAAS,IAGjC,EAAQ,WAH0C,GAG5B,GAE9B,EAAG,CAEC,iBAAiB,EACjB,eAAgB,EAAkB,cAAc,AACpD,GAEA,EAAkB,cAAc,EAAG,EAEnC,EAAQ,KAAK,EACjB,CACJ,CACJ,CACJ,CACA,SAAS,EAAsB,CAAK,EAChC,GAAI,aAAE,CAAW,CAAE,UAAQ,CAAE,CAAG,EAC1B,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EAAA,yBAAyB,EACpD,GAAI,CAAC,EACD,MAAM,CADI,MACG,cAAc,CAAC,AAAI,MAAM,8CAA+C,oBAAqB,CACtG,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,MAAqB,CAAd,AAAc,EAAA,EAAA,GAAA,AAAI,EAAC,EAA4B,AAApC,CACd,YAAa,EACb,kBAAmB,EAAQ,iBAAiB,CAC5C,SAAU,CACd,EACJ,CAGI,SAAS,EAAkB,CAAK,EAChC,GAAI,MAAE,CAAI,CAAE,aAAW,WAAE,CAAS,KAAE,CAAG,CAAE,CAAG,EACtC,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EAAA,yBAAyB,EACpD,GAAI,CAAC,EACD,MAAM,CADI,MACG,cAAc,CAAC,AAAI,MAAM,8CAA+C,oBAAqB,CACtG,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,GAAM,CAAE,KAAM,CAAQ,CAAE,CAAG,EAOrB,EAAgD,OAA1B,EAAU,WAAW,CAAY,EAAU,WAAW,CAAG,EAAU,GAAG,CAI5F,EAAM,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAU,GAAG,CAAE,GAKtC,EAA6B,UAAf,OAAO,GAA4B,OAAR,GAAoC,YAApB,OAAO,EAAI,IAAI,CAAkB,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,GAAO,EAC3G,GAAI,CAAC,EAAa,CAKd,IAAI,EAAW,EAAU,QAAQ,CACjC,GAAiB,OAAb,EAAmB,CAInB,IAAM,EAzPd,AAyP4B,SAzPnB,EAAe,CAAiB,CAAE,CAAc,EACzD,GAAI,EAAmB,CACnB,GAAM,CAAC,EAAS,EAAiB,CAAG,EAC9B,EAAsC,IAA7B,EAAkB,MAAM,CACvC,GAAI,EAAa,CAAc,CAAC,EAAE,CAAE,IAC5B,CAAc,CAAC,EAAE,CAAC,CADoB,aACN,CAAC,GAAmB,CACpD,GAAI,EAAQ,CACR,IAAM,EAAU,OAAe,EAAW,CAAc,CAAC,EAAE,CAAC,EAAiB,EAC7E,MAAO,CACH,CAAc,CAAC,EAAE,CACjB,CACI,GAAG,CAAc,CAAC,EAAE,CACpB,CAAC,EAAiB,CAAE,CAChB,CAAO,CAAC,EAAE,CACV,CAAO,CAAC,EAAE,CACV,CAAO,CAAC,EAAE,CACV,UACH,AACL,EACH,AACL,CACA,MAAO,CACH,CAAc,CAAC,EAAE,CACjB,CACI,GAAG,CAAc,CAAC,EAAE,CACpB,CAAC,EAAiB,CAAE,EAAe,EAAkB,KAAK,CAAC,GAAI,CAAc,CAAC,EAAE,CAAC,EAAiB,CACtG,EACH,AACL,CAER,CACA,OAAO,CACX,EAyN+C,CAC/B,MACG,EACN,CAAE,GACG,EmBlRX,AnBkR4B,SmBlRnB,EAAkC,CAAK,EACnD,GAAI,CAAC,EAAS,EAAe,CAAG,EAEhC,GAAI,MAAM,OAAO,CAAC,KAA4B,OAAhB,AAAC,CAAO,CAAC,EAAE,EAAa,AAAe,QAAR,CAAC,EAAO,AAAL,CAAS,EAIlD,CAJqD,SAIxE,EAA+B,KAAxB,QIE6F,IAAjG,AJFuD,EIElD,KAAK,CAAC,EJFsD,GIEjD,IAAI,CAAC,AAAC,GAAU,EAA2B,IAAI,CAAC,AAAC,GAAI,EAAQ,UAAU,CAAC,KJL3F,OAAO,EAOX,GAAI,GACA,IAAI,IAAM,KADM,AACC,EACb,GAAI,EAAkC,CAAc,CAAC,EAAI,EACrD,CADwD,CADhC,KAEjB,CAEf,CAEJ,MAAO,EACX,EnB+PqE,AmB7PrE,GnB8PkB,EAAc,KAAK,GAAG,EAC5B,GAAU,QAAQ,CAAG,EAAW,EAAoB,IAAI,IAAI,EAAK,SAAS,MAAM,EAAG,CAC/E,OmBhQkD,WnBgQ/B,EACnB,QAAS,EAAiB,EAAQ,OAAO,CAAG,IAChD,GAAG,IAAI,CAAE,AAAD,IACJ,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,KACZ,EAAwB,CACpB,KOxRW,CPwRL,cACN,aAAc,iBACd,cACA,CACJ,EACJ,GACO,IAGX,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EACR,CAGA,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EACR,CAcA,MAXc,CAAA,AAWP,EAXO,EAAA,GAAA,AAAI,EAAC,EAAA,mBAAmB,CAAC,QAAQ,CAAE,CAC7C,MAAO,CACH,WAAY,EACZ,gBAAiB,EACjB,kBAAmB,EAEnB,IAAK,CACT,EACA,SAAU,CACd,EAGJ,CAII,SAAS,EAAgB,CAAK,EAC9B,IASI,EATA,SAAE,CAAO,UAAE,CAAQ,CAAE,CAAG,EAgB5B,GAJI,CAIA,CANmB,UAAnB,OAAO,GAAoC,OAAZ,GAA4C,YAAxB,AAAoC,OAA7B,EAAQ,IAAI,CAElD,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,AADE,GAGN,EAED,CACnB,IAAM,EAAa,CAAiB,CAAC,EAAE,CACjC,EAAgB,CAAiB,CAAC,EAAE,CACpC,EAAiB,CAAiB,CAAC,EAAE,CAC3C,MAAqB,CAAd,AAAc,EAAA,EAAA,GAAI,AAAJ,EAAK,EAAA,AAAR,QAAgB,CAAE,CAChC,SAAwB,CAAd,AAAc,EAAA,EAAA,IAAA,AAAK,EAAC,CAAT,CAAS,QAAS,CAAE,CACrC,SAAU,CACN,EACA,EACA,EACH,AACL,GACA,SAAU,CACd,EACJ,CACA,MAAqB,CAAd,AAAc,EAAA,EAAA,GAAA,AAAI,EAAC,EAAR,AAAQ,QAAS,CAAE,CACjC,SAAU,CACd,EACJ,CAImB,SAAS,EAAkB,CAAK,EAC/C,GAAI,CAAE,mBAAiB,CAAE,OAAK,aAAE,CAAW,cAAE,CAAY,gBAAE,CAAc,iBAAE,CAAe,UAAE,CAAQ,UAAE,CAAQ,WAAE,CAAS,CAAE,cAAY,uBAAE,CAAqB,CAAE,CAAG,EAC7J,EAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EAAA,mBAAmB,EAC9C,GAAI,CAAC,EACD,MAAM,CADI,MACG,cAAc,CAAC,AAAI,MAAM,kDAAmD,oBAAqB,CAC1G,MAAO,MACP,YAAY,EACZ,cAAc,CAClB,GAEJ,GAAM,YAAE,CAAU,iBAAE,CAAe,mBAAE,CAAiB,KAAE,CAAG,CAAE,CAAG,EAG1D,EAAuB,EAAgB,cAAc,CACvD,EAAa,EAAqB,GAAG,CAAC,GAGrC,IACD,EAAa,IAAI,EADJ,EAEb,EAAqB,GAAG,CAAC,EAAmB,IAEhD,IAAM,EAAoB,CAAU,CAAC,EAAE,CACjC,EAAoC,OAAtB,AACpB,EACA,CACI,EACH,CAAG,EAAkB,MAAM,CAAC,CACzB,EACA,EACH,EAWK,EAAa,CAAU,CAAC,EAAE,CAAC,EAAkB,CAE7C,EAAiB,EADD,CAlBe,AAkBL,CAAC,EAAE,EACwB,GAQvD,Ee/XG,Af+XY,CAR8C,OAArB,CevX5B,AAAiB,CAAU,CAAE,CAAc,EAU3D,GAAM,CAAC,Ef6W6E,Ae7W5D,EAAmB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC9B,EACjB,KAAM,EACN,SAAU,EACV,KAAM,KACV,GAGJ,GAAI,EAAgB,IAAI,GAAK,EAGzB,OAAO,EAUX,CAbyC,GAanC,EAAiB,CACnB,KAAM,EACN,SAAU,EACV,KAAM,IACV,EAII,EAAI,EACJ,EAAW,EACX,EAAc,EAClB,KAAmB,OAAb,GAAqB,EA9DuC,EA8DnC,CAAqB,CAChD,GAAI,EAAS,QAAQ,GAAK,EAAgB,CAQtC,EAAY,IAAI,CAAG,EAAS,IAAI,CAChC,KACJ,CAAO,CAEH,IACA,IAAM,EAAQ,CACV,KAAM,EAAS,IAAI,CACnB,SAAU,EAAS,QAAQ,CAC3B,KAAM,IACV,EACA,EAAY,IAAI,CAAG,EACnB,EAAc,CAClB,CACA,EAAW,EAAS,IAAI,AAC5B,CAEA,OADA,EAAmB,GACZ,CACX,Ef2TwC,AezTxC,EfyToD,GAC5C,EAAW,EAAE,CACjB,EAAG,CACC,IAAM,EAAO,EAAa,IAAI,CACxB,EAAW,EAAa,Ke7TH,Gf6TW,CAEhC,EAAW,EADD,CAAI,CAAC,EAAE,EAGnB,EAAY,EAAW,GAAG,CAAC,GAC/B,EAHsC,MAGpB,IAAd,EAAyB,CAGzB,IAAM,EAAmB,CACrB,SAAU,KACV,IAAK,KACL,YAAa,KACb,KAAM,KACN,aAAc,KACd,eAAgB,IAAI,IACpB,QAAS,KACT,YAAa,CAAC,CAClB,EAEA,EAAY,EACZ,EAAW,GAAG,CAAC,EAAU,EAC7B,CA8BA,IAAM,EAAoB,EAAgB,OAAO,CAC7C,EAAsB,CAAA,EAAA,EAAA,CAAd,GAAc,AAAK,EAAC,EAAA,IAAT,WAAwB,CAAC,QAAQ,CAAE,CACtD,MAAqB,CAAd,AAAc,EAAA,EAAA,IAAA,AAAK,EAAC,CAAT,CAAgC,CAC9C,YAAa,EACb,SAAU,CACQ,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAe,CAC9B,eAAgB,EAChB,YAAa,EACb,aAAc,EACd,SAAwB,CAAd,AAAc,EAAA,EAAA,GAAA,AAAI,EAAC,EAAR,AAAyB,CAC1C,QAAS,EACT,SAAwB,CAAA,AAAd,EAAc,EAAA,GAAA,AAAI,EAAC,EAAA,AAAR,0BAAkC,CAAE,CACrD,SAAU,EACV,UAAW,EACX,aAAc,EACd,SAAwB,CAAA,AAAd,EAAc,EAAA,IAAA,AAAK,EAAC,CAAT,CAA2B,CAC5C,SAAU,CACQ,CAAA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAmB,CAClC,IAAK,EACL,KAAM,EACN,UAAW,EACX,YAAa,CACjB,GA1CF,KA4CD,AACL,EACJ,EACJ,EACJ,GA/Ce,KAiDlB,AACL,GACA,SAAU,CACN,EACA,EACA,EACH,AACL,EAAG,GAgBH,EAAS,IAAI,CAAC,GACd,EAAe,EAAa,IAAI,AACpC,OAAyB,OAAjB,EAAuB,AAC/B,OAAO,CACX,EAEA,yCAAyC,uBkCrgBzC,EAAmD,CAA5CoB,CAA4C,CAAA,CAAA,MAAnCC,CAChB,EAAgC,EAAA,CAAvBC,AAAuB,CAAA,GADN,GAGX,KAH6B,GACpB,CAEAC,EAF4D,EAGlF,AAJiD,GACnB,CAGxBC,EAAAA,CAAAA,EAAWH,EAAAA,UAAAA,EAAWC,EAAAA,eAAAA,EAC5B,MAAA,CAAA,EAAO,EAAA,GAAA,EAAA,EAAP,AAAO,QAAA,CAAA,UAAGE,GACZ,yEGRO,OAAM,EACT,OAAO,IAAI,CAAM,CAAE,CAAI,CAAE,CAAQ,CAAE,CAC/B,IAAM,EAAQ,QAAQ,GAAG,CAAC,EAAQ,EAAM,SACxC,AAAqB,YAAjB,AAA6B,OAAtB,EACA,EAAM,IAAI,CAAC,GAEf,CACX,CACA,OAAO,IAAI,CAAM,CAAE,CAAI,CAAE,CAAK,CAAE,CAAQ,CAAE,CACtC,OAAO,QAAQ,GAAG,CAAC,EAAQ,EAAM,EAAO,EAC5C,CACA,OAAO,IAAI,CAAM,CAAE,CAAI,CAAE,CACrB,OAAO,QAAQ,GAAG,CAAC,EAAQ,EAC/B,CACA,OAAO,eAAe,CAAM,CAAE,CAAI,CAAE,CAChC,OAAO,QAAQ,cAAc,CAAC,EAAQ,EAC1C,CACJ,EAEA,mCAAmC,6BFnBnC,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,QAAS,IACb,EAEM,EAA+B,YAAvB,OAAO,EAAA,KAAW,CAAkB,EAAA,KAAW,CAAG,AAAC,GAAK,EAIhE,EAAuE,QAAQ,IAAI,CAqB9E,EArBY,OAqBH,EAA4C,CAAU,EACtE,OAAO,SAAS,AAAgB,GAAG,CAAI,EAkB/B,EAjBY,EAvBqC,GAuBvB,GAmBlC,CACJ,CAxC+B,CA0C/B,CAzCA,AAAC,CAoC0B,GAnCvB,GAAI,CACA,EAAe,EAAS,OAAO,CACnC,QAAS,CACL,EAAS,OAAO,CAAG,IACvB,CACJ,6BAmC0E,8FCjD1E,IAAM,EAA+B,6BAC9B,SAAS,EAA6B,CAAM,CAAE,CAAI,SACrD,AAAI,EAA6B,IAAI,CAAC,GAC3B,IADkC,AAC5B,EAAS,IAAM,EAAO,IAEhC,IAAM,EAAS,IAAM,KAAK,SAAS,CAAC,GAAQ,IACvD,CACO,SAAS,EAAkC,CAAM,CAAE,CAAI,EAC1D,IAAM,EAAkB,KAAK,SAAS,CAAC,GACvC,MAAO,gBAAkB,EAAS,KAAO,EAAkB,QAAU,EAAkB,OAAS,EAAS,eAC7G,CACO,IAAM,EAAsB,IAAI,IAAI,CACvC,iBACA,gBACA,uBACA,WACA,UACA,iBAGA,OACA,QACA,UAGA,SAEA,cACA,aAGA,SACA,WACA,aACH,GAED,yCAAyC,uNGzCzC,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,ODNA,EAAA,EAAA,CAAA,CAAA,OASO,SAAS,EAAsD,CAAK,CAAE,CAAU,EACnF,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,qBAAqB,CAAC,CAAC,MAAM,EAAE,EAAM,4EAA4E,EAAE,EAAW,0HAA0H,CAAC,EAAG,oBAAqB,CAC7S,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,CACO,SAAS,EAAqC,CAAS,CAAE,CAAc,EAC1E,IAAM,EAAQ,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,MAAM,EAAE,EAAU,KAAK,CAAC,uXAAuX,CAAC,EAAG,oBAAqB,CACnd,MAAO,OACP,YAAY,EACZ,aAAc,EAClB,EAGA,OAFA,MAAM,iBAAiB,CAAC,EAAO,GAC/B,EAAU,wBAAwB,GAAK,EACjC,CACV,CCjBO,SAAS,EAA6B,CAAsB,CAAE,CAAS,EAC1E,IAAM,EAAgB,EAAA,oBAAoB,CAAC,QAAQ,GACnD,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,YACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACD,OAAO,EAAkC,EAAW,EACxD,KAAK,oBACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,6EAA8E,oBAAqB,CAC9I,MAAO,OACP,WAAY,GACZ,cAAc,CAClB,EACJ,KAAK,QACL,IAAK,gBACL,IAAK,iBACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,wEAAyE,oBAAqB,CACzI,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,KAAK,UACD,OAAO,EAAyB,EAAwB,EAGhE,CAEJ,CAAA,EAAA,EAAA,6BAAA,AAA6B,GACjC,CDrCA,EAAA,CAAA,CAAA,OCuCO,IAAM,EAAsC,EAC5C,SAAS,EAAsC,CAAsB,CAAE,CAAS,EACnF,IAAM,EAAgB,EAAA,oBAAoB,CAAC,QAAQ,GACnD,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,YACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACD,OAAO,EAAkC,EAAW,EACxD,KAAK,QACL,IAAK,gBACL,IAAK,iBACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,iFAAkF,oBAAqB,CAClJ,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,KAAK,wBAoE2B,IAnE5B,OAAO,EAAmC,EAmEU,EAnEc,EAoEvE,CAAA,EADuD,AACvD,EAAA,IADsE,kBACtE,AAAsB,EAAC,EAAyG,EAAgC,GAnE/J,KAAK,GAmEgC,OAlEjC,OAAO,EAAyB,EAAwB,EAGhE,CAEJ,CAAA,EAAA,EAAA,YA6DmF,iBA7DnF,AAA6B,GACjC,CACO,SAAS,EAAyC,CAAS,EAC9D,GAAI,EAAU,WAAW,CAGrB,CAHuB,MAGhB,QAAQ,OAAO,CAAC,CAAC,GAE5B,IAAM,EAAgB,EAAA,oBAAoB,CAAC,QAAQ,GACnD,GAAI,EACA,OAAO,EAAc,IADN,AACU,EACrB,IAAK,YACL,IAAK,mBAGD,MAAO,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAC,EAAc,YAAY,CAAE,EAAU,KAAK,CAAE,iBAC3E,KAAK,oBACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,yFAA0F,oBAAqB,CAC1J,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,KAAK,QACL,IAAK,gBACL,IAAK,iBACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,oFAAqF,oBAAqB,CACrJ,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,KAAK,gBACL,IAAK,mBACL,IAAK,UACD,OAAO,QAAQ,OAAO,CAAC,CAAC,EAGhC,CAEJ,CAAA,EAAA,EAAA,6BAAA,AAA6B,GACjC,CACA,SAAS,EAAkC,CAAS,CAAE,CAAc,EAChE,GAAI,EAAU,WAAW,CAGrB,CAHuB,MAGhB,QAAQ,OAAO,CAAC,CAAC,GAE5B,OAAO,EAAe,IAAI,EACtB,IAAK,YACL,IAAK,uBAuCoB,EArCU,EAqCC,EArCU,EAsClD,CADsC,GAChC,EAAqB,EAAmB,EADQ,CACL,CAAC,GAClD,GAAI,EACA,OAAO,EAEX,IAAM,EAAU,CAAA,EAAA,AAHQ,EAGR,kBAAA,AAAkB,EAAC,EAAe,YAAY,CAAE,EAAU,KAAK,CAAE,kBAC3E,EAAiB,IAAI,MAAM,EAAS,CACtC,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EACvB,GAAI,OAAO,MAAM,CAAC,EAAS,GAIvB,IAJ8B,GAIvB,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,GAE5C,OAAO,GACH,IAAK,OAIG,MADA,CAAA,EAAA,EAAA,qBAAA,AAAqB,EADF,AACG,wDAAY,GAC3B,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAEhD,KAAK,SAIG,MADA,CAAA,EAAA,EAAA,qBAAA,AAAqB,EADF,AACG,yDAAY,GAC3B,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAEhD,SAEQ,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAEpD,CACJ,CACJ,GAEA,OADA,EAAmB,GAAG,CAAC,EAAgB,GAChC,CAvEH,KAAK,gBACL,IAAK,uBAwE2B,EArEU,EAqEC,EArEU,EAsEzD,CAD6C,GACvC,EAAqB,EAAmB,EADe,CACZ,CAAC,GAClD,GAAI,EACA,OAAO,EAMX,IAAM,EAAU,GAPQ,KAOA,OAAO,CAJA,AAIC,CAJA,GAK1B,EAAiB,IAAI,MAAM,EAAS,CACtC,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EACvB,GAAI,OAAO,MAAM,CAAC,EAAS,GAIvB,IAJ8B,GAIvB,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,GAE5C,OAAO,GACH,IAAK,OACD,CACI,IAAM,EAAa,wDACf,EAAU,kBAAkB,CAC5B,CAD8B,CACwB,EAAU,KAAK,CAAE,GAChE,AAAwB,iBAAiB,GAA1B,IAAI,CAE1B,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,KAAK,CAAE,EAAY,EAAe,eAAe,EAGhF,CAAA,EAAA,EAAA,gCAAgC,AAAhC,EAAiC,EAAY,EAAW,GAE5D,MACJ,CACJ,IAAK,SACD,CACI,IAAM,EAAa,yDACf,EAAU,kBAAkB,CAC5B,CAD8B,CACwB,EAAU,KAAK,CAAE,GACxC,iBAAiB,CAAzC,EAAe,IAAI,CAE1B,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,KAAK,CAAE,EAAY,EAAe,eAAe,EAGhF,CAAA,EAAA,EAAA,gCAAA,AAAgC,EAAC,EAAY,EAAW,GAE5D,MACJ,CACJ,QAEQ,GAAoB,UAAhB,OAAO,GAAqB,CAAC,EAAA,mBAAmB,CAAC,GAAG,CAAC,GAAO,CAC5D,IAAM,EAAa,CAAA,EAAA,EAAA,4BAAA,AAA4B,EAAC,eAAgB,EAC5D,GAAU,kBAAkB,CAC5B,CAD8B,CACwB,EAAU,KAAK,CAAE,GACxC,iBAAiB,CAAzC,EAAe,IAAI,CAE1B,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,KAAK,CAAE,EAAY,EAAe,eAAe,EAGhF,CAAA,EAAA,EAAA,gCAAA,AAAgC,EAAC,EAAY,EAAW,EAEhE,CACA,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAEpD,CACJ,EACA,IAAK,CAAM,CAAE,CAAI,EAKb,GAAoB,UAAhB,OAAO,EAAmB,CAC1B,IAAM,EAAa,CAAA,EAAA,EAAA,iCAAA,AAAiC,EAAC,eAAgB,GAUrE,OATI,EAAU,kBAAkB,CAC5B,CAD8B,CACwB,EAAU,KAAK,CAAE,GACxC,iBAAiB,CAAzC,EAAe,IAAI,CAE1B,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,KAAK,CAAE,EAAY,EAAe,eAAe,EAGhF,CAAA,EAAA,EAAA,gCAAA,AAAgC,EAAC,EAAY,EAAW,IAErD,CACX,CACA,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EACtC,EACA,UACI,IAAM,EAAa,+DACf,EAAU,kBAAkB,CAC5B,CAD8B,CACwB,EAAU,KAAK,CAAE,GACxC,iBAAiB,CAAzC,EAAe,IAAI,CAE1B,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,KAAK,CAAE,EAAY,EAAe,eAAe,EAGhF,CAAA,EAAA,EAAA,gCAAA,AAAgC,EAAC,EAAY,EAAW,EAEhE,CACJ,GAEA,OADA,EAAmB,GAAG,CAAC,EAAW,GAC3B,CAvKH,SACI,OAAO,CACf,CACJ,CAIA,SAAS,EAAyB,CAAsB,CAAE,CAAS,SAC/D,AAAI,EAAU,WAAW,CAGd,CAHgB,OAGR,OAAO,CAAC,CAAC,GAcb,EAAgC,EAGnD,CACA,IAAM,EAAqB,IAAI,QACzB,EAAgC,IAAI,QA+I/B,SAAS,EAAoC,CAAS,EAC7D,IAAM,EAAqB,EAA8B,GAAG,CAAC,GAC7D,GAAI,EACA,OAAO,EAEX,IAAM,EAAU,GAHQ,KAGA,OAAO,CAAC,CAAC,GAC3B,EAAiB,IAAI,MAAM,EAAS,CACtC,IAAK,SAAS,EAAI,CAAM,CAAE,CAAI,CAAE,CAAQ,SAChC,OAAO,MAAM,CAAC,EAAS,IAOP,GAPc,OAO9B,EAA4B,KAArB,GAA+B,SAAT,CAAmB,EAAC,EAAA,mBAAmB,CAAC,GAAG,CAAC,IACzE,CAD8E,CACzC,EAD4C,AACjC,GAHzC,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAMhD,EACA,IAAK,SAAS,EAAI,CAAM,CAAE,CAAI,EAQ1B,MAHoB,UAAhB,EAA4B,KAArB,GAA+B,SAAT,CAAmB,EAAC,EAAA,mBAAmB,CAAC,GAAG,CAAC,IACzE,CAD8E,CACzC,EAD4C,AACjC,GAE7C,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EACtC,EACA,QAAS,SAAS,IACd,EAAqC,EAAW,EACpD,CACJ,GAEA,OADA,EAA8B,GAAG,CAAC,EAAW,GACtC,CACX,CACA,SAAS,EAAgC,CAAsB,EAC3D,IAAM,EAAqB,EAAmB,GAAG,CAAC,GAClD,GAAI,EACA,OAAO,EAKX,IAAM,EAAU,GANQ,KAMA,OAAO,CAAC,GAwBhC,OAvBA,EAAmB,GAAG,CAAC,EAAwB,GAC/C,OAAO,IAAI,CAAC,GAAwB,OAAO,CAAC,AAAC,IACrC,AAAC,EAAA,mBAAmB,CAAC,GAAG,CAAC,IACzB,GADgC,IACzB,cAAc,CAAC,EAAS,EAAM,CACjC,MACI,IAAM,EAAgB,EAAA,oBAAoB,CAAC,QAAQ,GAInD,OAHI,GACA,CAAA,EAAA,EAAA,OADe,wBACf,AAA+B,EAAC,GAE7B,CAAsB,CAAC,EAAK,AACvC,EACA,IAAK,CAAK,EACN,OAAO,cAAc,CAAC,EAAS,EAAM,OACjC,EACA,UAAU,EACV,YAAY,CAChB,EACJ,EACA,YAAY,EACZ,aAAc,EAClB,EAER,GACO,CACX,CAwN0B,CAAA,EAAA,EAAA,2CAAA,AAA2C,EAAC,AAEtE,SAAS,AAAwB,CAAK,CAAE,CAAU,EAC9C,IAAM,EAAS,EAAQ,CAAC,OAAO,EAAE,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAO,cAAc,CAAC,AAAI,MAAM,CAAA,EAAG,EAAO,KAAK,EAAE,EAAW,gIAAE,CAAC,EAA2I,CAAxI,CAAC,kBAA4J,CAClO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,CAL8I,CAAC,CAH1G,CAAA,CAG6G,CAH7G,AAG8G,EAH9G,2CAAA,AAA2C,EAShF,AATiF,SASxE,AAAiC,CAAK,CAAE,CAAU,CAAE,CAAiB,CANmI,CAO7M,AAP8M,IAOxM,EAAS,EAAQ,CAAC,OAAO,EAAE,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAO,cAAc,CAAC,AAAI,MAAM,CAAA,EAAG,EAAO,KAAK,EAAE,EAAiO,SAAtN,EAAE,wLAAoN,EAAG,AAMhS,SAAS,AAA4B,CAAU,EAC3C,OAAO,EAAW,MAAM,EACpB,KAAK,EACD,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,uFAAwF,oBAAqB,CACxJ,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EACJ,MAAK,EACD,MAAO,CAAC,EAAE,EAAE,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AACjC,MAAK,EACD,MAAO,CAAC,EAAE,EAAE,CAAU,CAAC,EAAE,CAAC,SAAS,EAAE,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAC1D,SACI,CACI,IAAI,EAAc,GAClB,IAAI,IAAI,EAAI,EAAG,EAAI,EAAW,MAAM,CAAG,EAAG,IAAI,AAC1C,GAAe,CAAC,EAAE,EAAE,CAAU,CAAC,EAAE,CAAC,IAAI,CAAC,CAG3C,OAAO,AADP,EAAe,CAAC,QAAQ,EAAE,CAAU,CAAC,EAAW,MAAM,CAAG,EAAE,CAAC,EAAE,CAElE,AAFmE,CAG3E,CACJ,EA5B4T,AA8B5T,GA9B+U,sCA8BtS,0BA9BwS,CAAC,AAAxQ,EAA8U,CAA3U,AAAwQ,CAAvQ,AAAwQ,kBAAuF,CACra,MAAO,KACP,YAAY,EACZ,cAAc,CAClB,EACJ,CALoZ,CAAC,CAAvQ,CAAC,GAAG,CAAC,gEAAgE,CAAC,GAAG,CAAC,iEAAiE,CAAC,GAAG,iFCjlB7R,IAAA,EAGO,EAAA,CAFLZ,AAEK,CAAA,OAGP,CAHkD,CAGnB,EAAA,CAAtBa,AAAsB,CAAA,EALb,CAKqD,IACvE,EAEEE,CANK,CAOLC,CAFAF,AAEAE,CAJqB,AAIrBA,OAIF,CAR+B,CAc7BE,EAA6B,CAL7BjB,AAOK,CAAA,CAbe,EACpBe,IAaF,EAA+B,EAHA,AAGkC,CAAxDrB,AAAwD,CAAA,KAf/B,CAOZ,CAStB,AAfEoB,CAWAG,AAVsC,CAgBtCE,CAfAH,CAgBK,CAFLE,AAEK,CAAA,AAJgB,IAA0C,GAKjE,CAL+B,CAO7BG,AAJmB,EAKd,CAFLD,AAEK,CAAA,IAJA,EAGa,AAnBI,CAqBxB,EAASE,AAV8C,EAUK,CAAnDA,AAAmD,CAAA,CADrD,AANuB,AAbvB,EAoB6C,AANlDH,IAOF,EAA0C,CANG,AAIV,CAEO,AADkB,CAClB,AAJd,AAInBI,CAAiC,CAHxCF,MAoCK,SAASG,EACdC,CAAwB,CACxBvB,CAAoB,CAxDkB,CA0DtC,CArCgC,GAA6D,AAqCvFG,EAAgBL,CAtCuF,CAsCvFA,CArCkB,mBAqClBA,CAAqBG,QAAQ,GACnD,GAAIE,EACF,OAAQA,EAAcC,IAAI,AADT,EAEf,IAAK,YACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACH,OAAOoB,EACLD,EACAvB,EACAG,EAEJ,KAAK,QACL,IAAK,gBACL,IAAK,iBACH,MAAM,OAAA,cAEL,CAFK,IAAIX,EAAAA,cAAAA,CACR,kEADI,oBAAA,OAAA,mBAAA,eAAA,EAEN,EACF,KAAK,oBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,EAAAA,cAAAA,CACR,uEADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,UAYD,OAAOsC,EAAyBP,EAItC,IAEFR,EAAAA,6BAAAA,GACF,CAIO,IAAMgB,EAAgCC,EAGtC,SAASC,EACdV,CAAwB,CACxBvB,CAAoB,EAEpB,IAAMG,EAAgBL,EAAAA,SAPuD,WAOvDA,CAAqBG,QAAQ,GACnD,GAAIE,EACF,OAAQA,EAAcC,IADL,AACS,EACxB,IAAK,YACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACH,OAAOoB,EACLD,EACAvB,EACAG,EAEJ,KAAK,QACL,IAAK,gBACL,IAAK,iBACH,MAAM,OAAA,cAEL,CAFK,IAAIX,EAAAA,cAAAA,CACR,sEADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,oBACH,OAAO0C,EAA6BX,EAAkBpB,EACxD,KAAK,UAYD,OAAO2B,AAmLRkB,EAnLiCzB,EAItC,IAEFR,EAAAA,gBA6KiCQ,aA7KjCR,GACF,CAEO,SAASiB,EACdT,CAAwB,CACxBvB,CAAoB,EAEpB,IAAMG,EAAgBL,EAAAA,oBAAAA,CAAqBG,QAAQ,GACnD,GAAIE,EACF,OAAQA,EAAcC,IADL,AACS,EACxB,IAAK,YACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACH,OAAOoB,EACLD,EACAvB,EACAG,EAEJ,KAAK,QACL,IAAK,gBACL,IAAK,iBACH,MAAM,OAAA,cAEL,CAFK,IAAIX,EAAAA,cAAAA,CACR,8EADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,oBACH,OAAO0C,EAA6BX,EAAkBpB,EACxD,KAAK,UAYD,OAAO2B,EAAyBP,EAItC,IAEFR,EAAAA,6BAAAA,GACF,CAEO,SAASoB,EACdZ,CAAwB,EAExB,IAAMvB,EAAYH,EAAAA,gBAAAA,CAAiBI,QAAQ,GAC3C,GAAI,CAACD,EACH,MAAM,GADQ,IACR,cAEL,CAFK,IAAIR,EAAAA,cAAAA,CACR,8DADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,IAAMW,EAAgBL,EAAAA,oBAAAA,CAAqBG,QAAQ,GACnD,GAAIE,EACF,OAAQA,EAAcC,IADL,AACS,EACxB,IAAK,YACL,IAAK,mBACH,IAAMgC,EAAiBjC,EAAckC,mBAAmB,CACxD,GAAID,GACF,IAAK,IAAIE,KAAOf,AADE,EAEhB,GAAIa,EAAe5D,GAAG,CAAC8D,GAKrB,GAN8B,AACH,GAK3B,CAAA,EAAOnB,EAAAA,kBAAAA,EACLhB,EAAcoC,YAAY,CAC1BvC,EAAUwC,KAAK,CACf,WAGN,CAEF,KACF,KAAK,QACL,IAAK,gBACL,IAAK,iBACH,MAAM,OAAA,cAEL,CAFK,IAAIhD,EAAAA,cAAAA,CACR,iFADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAQJ,CAKF,OAAOiD,QAAQC,OAAO,CAACnB,EACzB,CAEA,SAASC,EACPD,CAAwB,CACxBvB,CAAoB,CACpB2C,CAAoC,EAEpC,OAAQA,EAAevC,IAAI,EACzB,IAAK,YACL,IAAK,mBAAoB,CACvB,IAAMgC,EAAiBO,EAAeN,mBAAmB,CACzD,GAAID,EACF,KAAK,IAAME,KADO,AACAf,EAChB,GAAIa,EAAe5D,GAAG,CAAC8D,GAKdM,GANyB,AACL,EA4HrCrB,EAtHYA,EAuHZvB,EAtHYA,EAuHZ2C,EAtHYA,EAwHZ,CAHoB,GADI,AAIlBuB,EAAed,EAAaG,EAFQ,CAEL,CAAChC,GACtC,GAAI2C,EACF,OAAOA,EAGT,GAJkB,CAIZC,EAAU,IAAIH,MAAAA,CAAAA,EAClB7C,EAAAA,kBAAAA,EACEwB,EAAeJ,YAAY,CAC3BvC,EAAUwC,KAAK,CACf,YAEFc,GAKF,OAFAF,EAAagB,GAAG,CAAC7C,EAAkB4C,GAE5BA,CAxIKxB,CAGN,CAEF,KACF,CACA,IAAK,gBAAiB,CACpB,IAAMP,EAAiBO,EAAeN,mBAAmB,CACzD,GAAID,GACF,IAAK,IAAME,KADO,AACAf,EAChB,GAAIa,EAAe5D,GAAG,CAAC8D,GACrB,GAFgC,AACL,IACpBO,AA+HnB,SAASA,AACPtB,CAAwB,CACxBa,CAAmC,CACnCpC,CAAoB,CACpB2C,CAAwD,EAExD,IAAMuB,EAAed,EAAaG,GAAG,CAAChC,GACtC,GAAI2C,EACF,OAAOA,EAGT,GAJkB,CAIZG,EAAsB,CAAE,GAAG9C,CAAgB,AAAC,EAK5C4C,EAAU1B,QAAQC,OAAO,CAAC2B,GA6EhC,OA5EAjB,EAAagB,GAAG,CAAC7C,EAAkB4C,GAEnCpG,OAAOuG,IAAI,CAAC/C,GAAkBgD,OAAO,CAAC,AAACd,IACjCxC,EAAAA,mBAAAA,CAAoBzC,GAAG,CAACiF,KAItBrB,EAJ6B,AAId5D,GAAG,CAACiF,IACrB1F,GAD4B,IACrByG,cAAc,CAACH,EAAqBZ,EAAM,CAC/CF,MACE,IAAMkB,EAAAA,CAAAA,EAAazD,EAAAA,4BAAAA,EAA6B,SAAUyC,EAO9B,iBAAiB,EAAzCd,EAAevC,IAAI,IAErBQ,EAAAA,oBAAAA,EACEZ,EAAUwC,KAAK,CACfiC,EACA9B,EAAe+B,eAAe,KAIhC/D,EAAAA,gCAAAA,EACE8D,EACAzE,EACA2C,EAGN,EACAgC,YAAY,CACd,GACA5G,OAAOyG,cAAc,CAACL,EAASV,EAAM,CACnCF,MACE,IAAMkB,EAAAA,CAAAA,EAAazD,EAAAA,4BAAAA,EAA6B,SAAUyC,GAO9B,iBAAiB,CAAzCd,EAAevC,IAAI,IAErBQ,EAAAA,oBAAAA,EACEZ,EAAUwC,KAAK,CACfiC,EACA9B,EAAe+B,eAAe,KAIhC/D,EAAAA,gCAAAA,EACE8D,EACAzE,EACA2C,EAGN,EACAyB,IAAIQ,CAAQ,EACV7G,OAAOyG,cAAc,CAACL,EAASV,EAAM,CACnCoB,MAAOD,EACPE,UAAU,EACVH,YAAY,CACd,EACF,EACAA,WAAY,GACZI,aAAc,EAChB,IAEEZ,CAAe,CAACV,EAAK,CAAGlC,CAAgB,CAACkC,EAAK,CAGtD,GAEOU,CACT,EA5Nc5C,EACAa,EACApC,EACA2C,EAGN,CAGJ,CAKF,CAKE,OAAOK,EAA0BzB,EAErC,CAEA,SAASW,EACPX,CAAwB,CACxBpB,CAA0C,EAE1C,MAAA,CAAA,EAAOW,EAAAA,sBAAAA,EACLX,EAGI6C,EAA0BzB,GAElC,CAwCA,IAAM6B,EAAe,CA5CjB3B,GA4CqB4B,KA5Cb3B,GAAG,AA8CT4B,CA9CUR,CA8CkD,CAChES,IAAK,SAASA,AAAIC,CAAM,CAAEC,CAAI,CAAEC,CAAQ,EACtC,CAhDmC,EAgDtB,CA/CTX,QA+CAU,GAA4B,SA/CRlC,CA+CDkC,GAA6B,YAATA,EAAoB,CAC7D,IAAME,EAAiBjD,EAAAA,cAAAA,CAAe6C,GAAG,CAACC,EAAQC,EAAMC,GAExD,MAAO,CAAA,CACL,CAACD,EAAK,CAAE,CAAC,GAAGG,KACV,IAAMC,EAAQxC,EAAAA,yBAAAA,CAA0BpB,QAAQ,GAQhD,OANI4D,GACFA,EAAMC,EADG,aACY,CAACC,KAAK,CACzB,OAAA,cAA8D,CAA9D,AAAIhF,MAAM,CAAC,iDAAiD,CAA5D,AAA6D,oBAA7D,OAAA,mBAAA,gBAAA,CAA6D,IAI1D,IAAIiF,MACTL,EAAeM,KAAK,CAACT,EAAQI,GAC7BN,EAEJ,EACF,CAAA,AAAC,CAACG,EAAK,AACT,CAEA,OAAO/C,EAAAA,cAAAA,CAAe6C,GAAG,CAACC,EAAQC,EAAMC,EAC1C,CACF,EA0HA,SAASV,EAA0BzB,CAAwB,EACzD,IAAM2C,EAAed,EAAaG,GAAG,CAAChC,GACtC,GAAI2C,EACF,OAAOA,EAMT,GAPkB,CAOZC,EAAU1B,QAAQC,OAAO,CAACnB,GAYhC,OAXA6B,EAAagB,GAAG,CAAC7C,EAAkB4C,GAEnCpG,OAAOuG,IAAI,CAAC/C,GAAkBgD,OAAO,CAAC,AAACd,IACjCxC,EAAAA,mBAAAA,CAAoBzC,GAAG,CAACiF,KAIxBU,CAAe,CAJgB,AAIfV,EAAK,CAAGlC,CAAgB,CAACkC,EAAAA,AAAK,CAEpD,GAEOU,CACT,CA+KMsB,CAAAA,EAAoBrE,EAAAA,2CAAAA,EAO1B,AANE0E,SAMOA,AACPtD,CAAyB,CACzBiC,CAAkB,EAElB,IAAMpG,EAASmE,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAIN,CAJM,AAAIzD,MACT,CAAA,EAAGV,EAAO,KAAK,EAAEoG,EAAW,0HAAE,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,mBAAA,cAEwD,CAAC,CAFzD,CAIP,CADI,CAAC,AAEP,GAbMiB,CAAAA,EACJtE,EAAAA,2CAAAA,EAcF,AAd8C2E,SAe5CvD,AADOuD,AAJ4D,CAK1C,AAL2C,CAMpEtB,CAAkB,CAClBkB,CAAgC,EAEhC,IAAMtH,EAASmE,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAON,CAPM,AAAIzD,MACT,CAAA,EAAGV,EAAO,KAAK,EAAEoG,EAIf,SAJ0B,EAAE,oKAI5B,EAAGuB,AAKT,SAASA,AAA4BC,CAAyB,EAC5D,OAAQA,EAAWJ,MAAM,EACvB,KAAK,EACH,MAAM,OAAA,cAEL,CAFK,IAAIrG,EAAAA,cAAAA,CACR,uFADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,MAAK,EACH,MAAO,CAAC,EAAE,EAAEyG,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAC/B,MAAK,EACH,MAAO,CAAC,EAAE,EAAEA,CAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AACxD,SAAS,CACP,IAAIC,EAAc,GAClB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAWJ,MAAM,CAAG,EAAGM,IAAK,AAC9CD,GAAe,CAAC,EAAE,EAAED,CAAU,CAACE,EAAE,CAAC,IAAI,CAAC,CAGzC,OADAD,AACOA,EADQ,CAAC,QAAQ,EAAED,CAAU,CAACA,EAAWJ,MAAM,CAAG,EAAE,CAAC,EAAE,CAAC,AAEjE,CACF,CACF,EAxBqCF,GAAmB,gEAAE,CAJvB,AAIwB,EALlD,CAEH,AAIA,CAJC,AAIA,kBANE,OAAA,mBAAA,cAEwD,CAAC,CAFzD,CAOP,CADmE,AAH/D,CAKN,AAFsE,AAH/D,gEAAgE,CAAC,GAClE,CAAC,KCluBP,EAA+B,EAAkC,CAAxDnG,AAAwD,CAAA,OAY1D,MAZgB,GAYP4G,CAZiD,CAYlC,CAW9B,EAvB8B,AAYA,GAAA,WAC7BC,CAAS,IDqtB+C,CAAC,GACrD,MCrtBJC,CAAY,QACZC,CAAM,UAENC,CAAQ,CAMT,CAX8B,CAYM,EACjC,IAGIG,EACAC,EAJE,kBAAE/G,CAAgB,CAAE,CACxB6G,EAAQ,CAAA,CAAA,IAAA,GAMJ7C,EAAQhE,EAAiBI,CAhB4B,OAgBpB,GACvC,GAAI,CAAC4D,EACH,KADU,CACJ,OAAA,cAEL,CAFK,IAAIrE,EAAAA,cAAAA,CACR,4EADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAM,8BAAEqH,CAA4B,CAAE,CACpCH,EAAQ,CAAA,CAAA,IAAA,GACVC,EAAqBE,EAA6BP,EAAczC,GAEhE,GAAM,wBAAEvC,CAAsB,CAAE,CAC9BoF,EAAQ,CAAA,CAAA,IAAA,GAGV,OAFAE,AAEA,EAFetF,EAAuBiF,EAAQ1C,GAE9C,CAAA,CAAA,CAAO,EAAA,GAAA,EAACwC,EAAAA,CAAUE,OAAQK,EAAcN,aAAcK,GACxD,CAUF,MAVS,sECjDT,EAA+B,EAAkC,CAAxDnH,AAAwD,CAAA,OAY1D,MAZgB,GAYPwH,CAZiD,CAY/B,CAWjC,EAXiC,AAZH,GAYG,WAChCX,CAAS,OACTY,CAAK,QACLV,CAAM,SAENpC,CAAO,CAMR,CAXiC,CAYG,EACjC,IAGIyC,EAHE,kBAAE/G,CAAgB,CAAE,CACxB6G,EAAQ,CAAA,CAAA,IAAA,GAKJ7C,EAAQhE,EAAiBI,IAf4B,IAepB,GACvC,GAAI,CAAC4D,EACH,KADU,CACJ,OAAA,cAEL,CAFK,IAAIrE,EAAAA,cAAAA,CACR,sGADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAM,CAAE8B,wBAAsB,CAAE,CAC9BoF,EAAQ,CAAA,CAAA,IAAA,GAGV,OAAA,AAFAE,EAAetF,EAAuBiF,EAAQ1C,GAE9C,CAAA,CAAA,CAAO,EAAA,GAAA,EAACwC,EAAAA,CAAW,GAAGY,CAAK,CAAEV,OAAQK,GACvC,CAMF,MANS,6DCrCF,IAAMM,EAAW,IAItB,CAAA,EAAO,EAAA,GAAA,EAACC,OAAAA,CAAKvH,KAAK,2FCVpB,EAAqC,EAAA,CAAA,AAA5BwH,CAA4B,OAGrC,AAHiB,EAAEC,GAAG,IAGbC,EAAe,CAIvB,CAP6B,CAGN,GAAA,IAHa,KAInCnD,CAAO,CAGR,CAJuB,EAKhB,CAAEhG,OAAK,QAAEC,CAAM,CAAE,CAAGiJ,CAAAA,EAAAA,EAAAA,GAAAA,EAAIlD,GAC9B,GAAIhG,EAMF,KANS,CACLC,GAGAD,GAAcC,EAHN,IAGY,CAAGA,CAAAA,EAErBD,EAER,OAAO,IACT,CAEO,SAASoJ,EAAoB,CAInC,EAJmC,GAAA,SAClCpD,CAAO,CAGR,CAJmC,EAKlC,MAAA,CAAA,EACE,EAAA,GAAA,EAACiD,EADH,AACGA,QAAAA,CAAAA,CAASI,SAAU,cAClB,CAAA,EAAA,EAAA,GAAA,EAACF,EAAD,AAACA,CAAenD,QAASA,KAG/B,qIC7BA,IAAA,EAEE9E,EACAC,CAAAA,AAFAF,CAEAE,MADsB,CAOxB,CANEA,AAAoB,EACpBC,CAKIkI,EAAY,CAChB,CAACrI,EAAAA,IATqB,EACtBC,YAEyB,IAMxBD,CAAuB,CAAE,EALrB,OAK+B,UAAEqB,CAAQ,CAA2B,EACvE,EANyB,KAMlBA,CACT,EACA,CAACpB,EAAAA,sBAAAA,CAAuB,CAAE,SAAU,UAAEoB,CAAQ,CAA2B,EACvE,OAAOA,CACT,EACA,CAACnB,EAAAA,oBAAAA,CAAqB,CAAE,SAAU,UAAEmB,CAAQ,CAA2B,EACrE,OAAOA,CACT,EACA,CAAClB,EAAAA,yBAAAA,CAA0B,CAAE,SAAU,UACrCkB,CAAQ,CAGT,EACC,OAAOA,CACT,CACF,EAEaiH,EAGXD,CAAS,CAACrI,EAAAA,aAFV,AACA,SACUA,CAAuBuI,KAAK,CAAC,GAAoC,CAAA,AAEhEC,EAGXH,CAAS,CAACpI,EAAAA,aAFV,AACA,SACUA,CAAuBsI,KAAK,CAAC,GAAoC,CAAA,AAEhEE,CARiD,CAW5DJ,CAAS,CAACnI,EAAAA,WAFV,AACA,IAXgF,KAYtEA,CAAqBqI,KAAK,CAAC,GAAkC,CAAA,AAE5DG,EAGXL,CAX4D,AAWnD,CACPlI,EAAAA,eAHF,AACA,EAXgF,QAa9EA,CAA0BoI,KAAK,CAAC,EAP0B,CAQ3D,CAAA,kBAT+E,qBAMpB,oBADoB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70]}