const features = [
  {
    title: 'Uncompromising Quality',
    description: 'Every project is completed to the highest standards with attention to detail that sets us apart.',
    icon: '⭐'
  },
  {
    title: 'Fully Managed Projects',
    description: 'From initial consultation to final handover, we manage every aspect of your renovation.',
    icon: '📋'
  },
  {
    title: 'Expert & Insured',
    description: 'Fully qualified craftsman with comprehensive insurance for your peace of mind.',
    icon: '🛡️'
  },
  {
    title: 'Transparent Pricing',
    description: 'Clear, upfront pricing with no hidden costs. You know exactly what you\'re paying for.',
    icon: '💰'
  }
]

export default function WhyChooseUs() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4">
            Why <PERSON><PERSON> Home Renovations?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            With years of experience and a commitment to excellence, we deliver renovations 
            that exceed expectations and stand the test of time.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="text-5xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-heading font-semibold text-kozak-charcoal mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
