{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/server/instrumentation/utils.ts", "turbopack:///[project]/node_modules/next/dist/esm/server/web/spec-extension/adapters/headers.js", "turbopack:///[project]/node_modules/next/dist/esm/server/base-http/index.js", "turbopack:///[project]/node_modules/next/dist/esm/server/api-utils/index.js", "turbopack:///[project]/node_modules/next/dist/esm/server/base-http/node.js", "turbopack:///[project]/node_modules/next/dist/esm/server/api-utils/get-cookie-parser.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/app-paths.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/page-path/ensure-leading-slash.js", "turbopack:///[project]/node_modules/next/dist/src/server/lib/cache-control.ts", "turbopack:///[project]/src/components/Hero.tsx", "turbopack:///[project]/src/components/ServicesOverview.tsx", "turbopack:///[project]/src/components/WhyChooseUs.tsx", "turbopack:///[project]/src/components/GalleryPreview.tsx", "turbopack:///[project]/src/components/TestimonialSnippet.tsx", "turbopack:///[project]/src/components/CTABanner.tsx", "turbopack:///[project]/src/app/page.tsx"], "sourcesContent": ["export function getRevalidateReason(params: {\n  isOnDemandRevalidate?: boolean\n  isRevalidate?: boolean\n}): 'on-demand' | 'stale' | undefined {\n  if (params.isOnDemandRevalidate) {\n    return 'on-demand'\n  }\n  if (params.isRevalidate) {\n    return 'stale'\n  }\n  return undefined\n}\n", "import { ReflectAdapter } from './reflect';\n/**\n * @internal\n */ export class ReadonlyHeadersError extends Error {\n    constructor(){\n        super('Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers');\n    }\n    static callable() {\n        throw new ReadonlyHeadersError();\n    }\n}\nexport class HeadersAdapter extends Headers {\n    constructor(headers){\n        // We've already overridden the methods that would be called, so we're just\n        // calling the super constructor to ensure that the instanceof check works.\n        super();\n        this.headers = new Proxy(headers, {\n            get (target, prop, receiver) {\n                // Because this is just an object, we expect that all \"get\" operations\n                // are for properties. If it's a \"get\" for a symbol, we'll just return\n                // the symbol.\n                if (typeof prop === 'symbol') {\n                    return ReflectAdapter.get(target, prop, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return undefined.\n                if (typeof original === 'undefined') return;\n                // If the original casing exists, return the value.\n                return ReflectAdapter.get(target, original, receiver);\n            },\n            set (target, prop, value, receiver) {\n                if (typeof prop === 'symbol') {\n                    return ReflectAdapter.set(target, prop, value, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, use the prop as the key.\n                return ReflectAdapter.set(target, original ?? prop, value, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return false.\n                if (typeof original === 'undefined') return false;\n                // If the original casing exists, return true.\n                return ReflectAdapter.has(target, original);\n            },\n            deleteProperty (target, prop) {\n                if (typeof prop === 'symbol') return ReflectAdapter.deleteProperty(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return true.\n                if (typeof original === 'undefined') return true;\n                // If the original casing exists, delete the property.\n                return ReflectAdapter.deleteProperty(target, original);\n            }\n        });\n    }\n    /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */ static seal(headers) {\n        return new Proxy(headers, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'append':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyHeadersError.callable;\n                    default:\n                        return ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n    /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */ merge(value) {\n        if (Array.isArray(value)) return value.join(', ');\n        return value;\n    }\n    /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */ static from(headers) {\n        if (headers instanceof Headers) return headers;\n        return new HeadersAdapter(headers);\n    }\n    append(name, value) {\n        const existing = this.headers[name];\n        if (typeof existing === 'string') {\n            this.headers[name] = [\n                existing,\n                value\n            ];\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            this.headers[name] = value;\n        }\n    }\n    delete(name) {\n        delete this.headers[name];\n    }\n    get(name) {\n        const value = this.headers[name];\n        if (typeof value !== 'undefined') return this.merge(value);\n        return null;\n    }\n    has(name) {\n        return typeof this.headers[name] !== 'undefined';\n    }\n    set(name, value) {\n        this.headers[name] = value;\n    }\n    forEach(callbackfn, thisArg) {\n        for (const [name, value] of this.entries()){\n            callbackfn.call(thisArg, value, name, this);\n        }\n    }\n    *entries() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(name);\n            yield [\n                name,\n                value\n            ];\n        }\n    }\n    *keys() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            yield name;\n        }\n    }\n    *values() {\n        for (const key of Object.keys(this.headers)){\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(key);\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n}\n\n//# sourceMappingURL=headers.js.map", "import { RedirectStatusCode } from '../../client/components/redirect-status-code';\nimport { getCookieParser } from '../api-utils/get-cookie-parser';\nexport class BaseNextRequest {\n    constructor(method, url, body){\n        this.method = method;\n        this.url = url;\n        this.body = body;\n    }\n    // Utils implemented using the abstract methods above\n    get cookies() {\n        if (this._cookies) return this._cookies;\n        return this._cookies = getCookieParser(this.headers)();\n    }\n}\nexport class BaseNextResponse {\n    constructor(destination){\n        this.destination = destination;\n    }\n    // Utils implemented using the abstract methods above\n    redirect(destination, statusCode) {\n        this.setHeader('Location', destination);\n        this.statusCode = statusCode;\n        // Since IE11 doesn't support the 308 header add backwards\n        // compatibility using refresh header\n        if (statusCode === RedirectStatusCode.PermanentRedirect) {\n            this.setHeader('Refresh', `0;url=${destination}`);\n        }\n        return this;\n    }\n}\n\n//# sourceMappingURL=index.js.map", "import { HeadersAdapter } from '../web/spec-extension/adapters/headers';\nimport { PRERENDER_REVALIDATE_HEADER, PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER } from '../../lib/constants';\nimport { getTracer } from '../lib/trace/tracer';\nimport { NodeSpan } from '../lib/trace/constants';\nexport function wrapApiHandler(page, handler) {\n    return (...args)=>{\n        getTracer().setRootSpanAttribute('next.route', page);\n        // Call API route method\n        return getTracer().trace(NodeSpan.runHandler, {\n            spanName: `executing api route (pages) ${page}`\n        }, ()=>handler(...args));\n    };\n}\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */ export function sendStatusCode(res, statusCode) {\n    res.statusCode = statusCode;\n    return res;\n}\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */ export function redirect(res, statusOrUrl, url) {\n    if (typeof statusOrUrl === 'string') {\n        url = statusOrUrl;\n        statusOrUrl = 307;\n    }\n    if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n        throw Object.defineProperty(new Error(`Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`), \"__NEXT_ERROR_CODE\", {\n            value: \"E389\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    res.writeHead(statusOrUrl, {\n        Location: url\n    });\n    res.write(url);\n    res.end();\n    return res;\n}\nexport function checkIsOnDemandRevalidate(req, previewProps) {\n    const headers = HeadersAdapter.from(req.headers);\n    const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER);\n    const isOnDemandRevalidate = previewModeId === previewProps.previewModeId;\n    const revalidateOnlyGenerated = headers.has(PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER);\n    return {\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated\n    };\n}\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`;\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`;\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024;\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA);\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS);\nexport function clearPreviewData(res, options = {}) {\n    if (SYMBOL_CLEARED_COOKIES in res) {\n        return res;\n    }\n    const { serialize } = require('next/dist/compiled/cookie');\n    const previous = res.getHeader('Set-Cookie');\n    res.setHeader(`Set-Cookie`, [\n        ...typeof previous === 'string' ? [\n            previous\n        ] : Array.isArray(previous) ? previous : [],\n        serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        }),\n        serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        })\n    ]);\n    Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n        value: true,\n        enumerable: false\n    });\n    return res;\n}\n/**\n * Custom error class\n */ export class ApiError extends Error {\n    constructor(statusCode, message){\n        super(message);\n        this.statusCode = statusCode;\n    }\n}\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */ export function sendError(res, statusCode, message) {\n    res.statusCode = statusCode;\n    res.statusMessage = message;\n    res.end(message);\n}\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */ export function setLazyProp({ req }, prop, getter) {\n    const opts = {\n        configurable: true,\n        enumerable: true\n    };\n    const optsReset = {\n        ...opts,\n        writable: true\n    };\n    Object.defineProperty(req, prop, {\n        ...opts,\n        get: ()=>{\n            const value = getter();\n            // we set the property on the object to avoid recalculating it\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n            return value;\n        },\n        set: (value)=>{\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n        }\n    });\n}\n\n//# sourceMappingURL=index.js.map", "import { SYMBOL_CLEARED_COOKIES } from '../api-utils';\nimport { NEXT_REQUEST_META } from '../request-meta';\nimport { BaseNextRequest, BaseNextResponse } from './index';\nlet prop;\nexport class NodeNextRequest extends BaseNextRequest {\n    static #_ = prop = _NEXT_REQUEST_META = NEXT_REQUEST_META;\n    constructor(_req){\n        var _this__req;\n        super(_req.method.toUpperCase(), _req.url, _req), this._req = _req, this.headers = this._req.headers, this.fetchMetrics = (_this__req = this._req) == null ? void 0 : _this__req.fetchMetrics, this[_NEXT_REQUEST_META] = this._req[NEXT_REQUEST_META] || {}, this.streaming = false;\n    }\n    get originalRequest() {\n        // Need to mimic these changes to the original req object for places where we use it:\n        // render.tsx, api/ssg requests\n        this._req[NEXT_REQUEST_META] = this[NEXT_REQUEST_META];\n        this._req.url = this.url;\n        this._req.cookies = this.cookies;\n        return this._req;\n    }\n    set originalRequest(value) {\n        this._req = value;\n    }\n    /**\n   * Returns the request body as a Web Readable Stream. The body here can only\n   * be read once as the body will start flowing as soon as the data handler\n   * is attached.\n   *\n   * @internal\n   */ stream() {\n        if (this.streaming) {\n            throw Object.defineProperty(new Error('Invariant: NodeNextRequest.stream() can only be called once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E467\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        this.streaming = true;\n        return new ReadableStream({\n            start: (controller)=>{\n                this._req.on('data', (chunk)=>{\n                    controller.enqueue(new Uint8Array(chunk));\n                });\n                this._req.on('end', ()=>{\n                    controller.close();\n                });\n                this._req.on('error', (err)=>{\n                    controller.error(err);\n                });\n            }\n        });\n    }\n}\nexport class NodeNextResponse extends BaseNextResponse {\n    get originalResponse() {\n        if (SYMBOL_CLEARED_COOKIES in this) {\n            this._res[SYMBOL_CLEARED_COOKIES] = this[SYMBOL_CLEARED_COOKIES];\n        }\n        return this._res;\n    }\n    constructor(_res){\n        super(_res), this._res = _res, this.textBody = undefined;\n    }\n    get sent() {\n        return this._res.finished || this._res.headersSent;\n    }\n    get statusCode() {\n        return this._res.statusCode;\n    }\n    set statusCode(value) {\n        this._res.statusCode = value;\n    }\n    get statusMessage() {\n        return this._res.statusMessage;\n    }\n    set statusMessage(value) {\n        this._res.statusMessage = value;\n    }\n    setHeader(name, value) {\n        this._res.setHeader(name, value);\n        return this;\n    }\n    removeHeader(name) {\n        this._res.removeHeader(name);\n        return this;\n    }\n    getHeaderValues(name) {\n        const values = this._res.getHeader(name);\n        if (values === undefined) return undefined;\n        return (Array.isArray(values) ? values : [\n            values\n        ]).map((value)=>value.toString());\n    }\n    hasHeader(name) {\n        return this._res.hasHeader(name);\n    }\n    getHeader(name) {\n        const values = this.getHeaderValues(name);\n        return Array.isArray(values) ? values.join(',') : undefined;\n    }\n    getHeaders() {\n        return this._res.getHeaders();\n    }\n    appendHeader(name, value) {\n        const currentValues = this.getHeaderValues(name) ?? [];\n        if (!currentValues.includes(value)) {\n            this._res.setHeader(name, [\n                ...currentValues,\n                value\n            ]);\n        }\n        return this;\n    }\n    body(value) {\n        this.textBody = value;\n        return this;\n    }\n    send() {\n        this._res.end(this.textBody);\n    }\n    onClose(callback) {\n        this.originalResponse.on('close', callback);\n    }\n}\nvar _NEXT_REQUEST_META;\n\n//# sourceMappingURL=node.js.map", "/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */ export function getCookieParser(headers) {\n    return function parseCookie() {\n        const { cookie } = headers;\n        if (!cookie) {\n            return {};\n        }\n        const { parse: parseCookieFn } = require('next/dist/compiled/cookie');\n        return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie);\n    };\n}\n\n//# sourceMappingURL=get-cookie-parser.js.map", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash';\nimport { isGroupSegment } from '../../segment';\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */ export function normalizeAppPath(route) {\n    return ensureLeadingSlash(route.split('/').reduce((pathname, segment, index, segments)=>{\n        // Empty segments are ignored.\n        if (!segment) {\n            return pathname;\n        }\n        // Groups are ignored.\n        if (isGroupSegment(segment)) {\n            return pathname;\n        }\n        // Parallel segments are ignored.\n        if (segment[0] === '@') {\n            return pathname;\n        }\n        // The last segment (if it's a leaf) should be ignored.\n        if ((segment === 'page' || segment === 'route') && index === segments.length - 1) {\n            return pathname;\n        }\n        return pathname + \"/\" + segment;\n    }, ''));\n}\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */ export function normalizeRscURL(url) {\n    return url.replace(/\\.rsc($|\\?)/, // $1 ensures `?` is preserved\n    '$1');\n}\n\n//# sourceMappingURL=app-paths.js.map", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */ export function ensureLeadingSlash(path) {\n    return path.startsWith('/') ? path : \"/\" + path;\n}\n\n//# sourceMappingURL=ensure-leading-slash.js.map", "import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n", "import Link from 'next/link'\n\nexport default function Hero() {\n  return (\n    <section className=\"relative bg-gradient-to-r from-kozak-charcoal to-gray-800 text-white\">\n      <div className=\"absolute inset-0 bg-black opacity-50\"></div>\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-heading font-bold mb-6\">\n            Master Craftsmanship in Every Detail\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 text-gray-200\">\n            Your Vision, Built to Last\n          </p>\n          <p className=\"text-lg mb-10 max-w-3xl mx-auto text-gray-300\">\n            Professional home renovations across Nottinghamshire, Derbyshire, and Leicestershire. \n            From kitchen fitting to loft conversions, we deliver uncompromising quality with traditional craftsmanship values.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/contact\" className=\"btn-primary text-lg px-8 py-4\">\n              Get Your Free Quote\n            </Link>\n            <Link href=\"/our-work\" className=\"btn-secondary text-lg px-8 py-4\">\n              View Our Work\n            </Link>\n          </div>\n        </div>\n      </div>\n      \n      {/* Background placeholder - replace with actual hero image */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"w-full h-full bg-gradient-to-br from-kozak-charcoal via-gray-700 to-kozak-orange opacity-20\"></div>\n      </div>\n    </section>\n  )\n}\n", "import Link from 'next/link'\n\nconst services = [\n  {\n    title: 'Kitchen Fitting',\n    description: 'Complete kitchen transformations from design to installation',\n    href: '/services/kitchen-fitting',\n    icon: '🏠'\n  },\n  {\n    title: 'Bathroom Renovations',\n    description: 'Full bathroom renovations from brickwork up',\n    href: '/services/bathroom-renovations',\n    icon: '🚿'\n  },\n  {\n    title: 'Loft Conversions',\n    description: 'Transform your loft into valuable living space',\n    href: '/services/loft-conversions',\n    icon: '🏗️'\n  },\n  {\n    title: 'External Wall Insulation',\n    description: 'Improve energy efficiency and exterior finish',\n    href: '/services/external-wall-insulation',\n    icon: '🏡'\n  },\n  {\n    title: 'General Renovations',\n    description: 'Windows, doors, floors, stairs, plastering, tiling, painting',\n    href: '/services/general-renovations',\n    icon: '🔨'\n  }\n]\n\nexport default function ServicesOverview() {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Our Services\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            From complete renovations to specialized installations, we deliver exceptional craftsmanship \n            across all aspects of home improvement.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <Link\n              key={index}\n              href={service.href}\n              className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 group\"\n            >\n              <div className=\"text-4xl mb-4\">{service.icon}</div>\n              <h3 className=\"text-xl font-heading font-semibold text-kozak-charcoal mb-3 group-hover:text-kozak-orange transition-colors\">\n                {service.title}\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                {service.description}\n              </p>\n              <div className=\"text-kozak-orange font-semibold group-hover:underline\">\n                Learn More →\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n", "const features = [\n  {\n    title: 'Uncompromising Quality',\n    description: 'Every project is completed to the highest standards with attention to detail that sets us apart.',\n    icon: '⭐'\n  },\n  {\n    title: 'Fully Managed Projects',\n    description: 'From initial consultation to final handover, we manage every aspect of your renovation.',\n    icon: '📋'\n  },\n  {\n    title: 'Expert & Insured',\n    description: 'Fully qualified craftsman with comprehensive insurance for your peace of mind.',\n    icon: '🛡️'\n  },\n  {\n    title: 'Transparent Pricing',\n    description: 'Clear, upfront pricing with no hidden costs. You know exactly what you\\'re paying for.',\n    icon: '💰'\n  }\n]\n\nexport default function WhyChooseUs() {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Why <PERSON><PERSON> Home Renovations?\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            With years of experience and a commitment to excellence, we deliver renovations \n            that exceed expectations and stand the test of time.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"text-center\">\n              <div className=\"text-5xl mb-4\">{feature.icon}</div>\n              <h3 className=\"text-xl font-heading font-semibold text-kozak-charcoal mb-3\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n", "import Link from 'next/link'\n\n// Placeholder gallery items - replace with actual project images\nconst galleryItems = [\n  {\n    id: 1,\n    title: 'Modern Kitchen Renovation',\n    category: 'Kitchen',\n    image: '/placeholder-kitchen.jpg',\n    alt: 'Modern grey shaker kitchen renovation'\n  },\n  {\n    id: 2,\n    title: 'Luxury Bathroom Transformation',\n    category: 'Bathroom',\n    image: '/placeholder-bathroom.jpg',\n    alt: 'Luxury bathroom renovation with modern fixtures'\n  },\n  {\n    id: 3,\n    title: 'Loft Conversion',\n    category: 'Loft',\n    image: '/placeholder-loft.jpg',\n    alt: 'Spacious loft conversion with natural light'\n  },\n  {\n    id: 4,\n    title: 'External Wall Insulation',\n    category: 'Insulation',\n    image: '/placeholder-insulation.jpg',\n    alt: 'External wall insulation installation'\n  },\n  {\n    id: 5,\n    title: 'Complete Home Renovation',\n    category: 'General',\n    image: '/placeholder-general.jpg',\n    alt: 'Complete home renovation project'\n  },\n  {\n    id: 6,\n    title: 'Custom Flooring Installation',\n    category: 'General',\n    image: '/placeholder-flooring.jpg',\n    alt: 'Custom hardwood flooring installation'\n  }\n]\n\nexport default function GalleryPreview() {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-heading font-bold text-kozak-charcoal mb-4\">\n            Our Recent Work\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Take a look at some of our recent projects. Each one showcases our commitment \n            to quality craftsmanship and attention to detail.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          {galleryItems.map((item) => (\n            <div key={item.id} className=\"group cursor-pointer\">\n              <div className=\"relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300\">\n                {/* Placeholder for actual images */}\n                <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n                  <div className=\"text-center text-gray-500\">\n                    <div className=\"text-2xl mb-2\">📷</div>\n                    <div className=\"text-sm font-medium\">{item.title}</div>\n                    <div className=\"text-xs\">{item.category}</div>\n                  </div>\n                </div>\n                <div className=\"absolute inset-0 bg-black opacity-0 group-hover:opacity-30 transition-opacity duration-300\"></div>\n                <div className=\"absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <h3 className=\"font-semibold\">{item.title}</h3>\n                  <p className=\"text-sm\">{item.category}</p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"text-center\">\n          <Link href=\"/our-work\" className=\"btn-primary\">\n            View Full Gallery\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n", "export default function TestimonialSnippet() {\n  return (\n    <section className=\"py-16 bg-kozak-charcoal text-white\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"mb-8\">\n          <div className=\"text-kozak-orange text-6xl mb-4\">&ldquo;</div>\n          <blockquote className=\"text-xl md:text-2xl font-light italic mb-6\">\n            <PERSON><PERSON> transformed our outdated kitchen into a stunning modern space. His attention to detail\n            and quality of work exceeded our expectations. The project was completed on time and within budget.\n            We couldn&apos;t be happier with the results!\n          </blockquote>\n          <div className=\"text-lg\">\n            <div className=\"font-semibold\"><PERSON> & <PERSON>.</div>\n            <div className=\"text-gray-300\">Kitchen Renovation, Nottingham</div>\n          </div>\n        </div>\n        \n        <div className=\"flex justify-center mb-6\">\n          <div className=\"flex space-x-1\">\n            {[...Array(5)].map((_, i) => (\n              <span key={i} className=\"text-kozak-orange text-2xl\">★</span>\n            ))}\n          </div>\n        </div>\n\n        <p className=\"text-gray-300 mb-8\">\n          Join our growing list of satisfied customers across Nottinghamshire, Derbyshire, and Leicestershire.\n        </p>\n\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <a href=\"/testimonials\" className=\"btn-primary\">\n            Read More Reviews\n          </a>\n          <a href=\"/contact\" className=\"btn-secondary bg-transparent border-2 border-white hover:bg-white hover:text-kozak-charcoal\">\n            Share Your Experience\n          </a>\n        </div>\n      </div>\n    </section>\n  )\n}\n", "import Link from 'next/link'\n\nexport default function CTABanner() {\n  return (\n    <section className=\"py-16 bg-kozak-orange text-white\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h2 className=\"text-3xl md:text-4xl font-heading font-bold mb-4\">\n          Ready to Transform Your Home?\n        </h2>\n        <p className=\"text-xl mb-8 opacity-90\">\n          Get your free consultation today and discover how we can bring your vision to life \n          with our expert craftsmanship and attention to detail.\n        </p>\n        \n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-8\">\n          <Link href=\"/contact\" className=\"bg-white text-kozak-orange hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 text-lg\">\n            Get Free Quote\n          </Link>\n          <a href=\"tel:07849768183\" className=\"bg-transparent border-2 border-white hover:bg-white hover:text-kozak-orange font-semibold py-4 px-8 rounded-lg transition-colors duration-200 text-lg\">\n            Call Now: 07849768183\n          </a>\n        </div>\n\n        <div className=\"text-sm opacity-75\">\n          <p>Serving Nottinghamshire, Derbyshire & Leicestershire</p>\n          <p>Monday - Friday: 8:00 AM - 8:00 PM</p>\n        </div>\n      </div>\n    </section>\n  )\n}\n", "import Hero from '@/components/Hero'\nimport ServicesOverview from '@/components/ServicesOverview'\nimport WhyChooseUs from '@/components/WhyChooseUs'\nimport GalleryPreview from '@/components/GalleryPreview'\nimport TestimonialSnippet from '@/components/TestimonialSnippet'\nimport CTABanner from '@/components/CTABanner'\n\nexport default function Home() {\n  return (\n    <div>\n      <Hero />\n      <ServicesOverview />\n      <WhyChooseUs />\n      <GalleryPreview />\n      <TestimonialSnippet />\n      <CTABanner />\n    </div>\n  );\n}\n"], "names": ["getRevalidateReason", "params", "isOnDemandRevalidate", "isRevalidate", "undefined", "CACHE_ONE_YEAR", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>"], "mappings": "8LAAO,SAASA,EAAoBC,CAGnC,SACKA,AAAJ,EAAWC,oBAAoB,CACtB,CADwB,WAG7BD,EAAOE,YAAY,CACd,CADgB,aAI3B,0HCXA,IG0HI,EH1HJ,EAAA,EAAA,CAAA,CAAA,MAGW,MGyHX,CHzHiB,UAA6B,MAC1C,aAAa,CACT,CGuHwB,IHvHnB,CAAC,qGACV,CACA,OAAO,UAAW,CACd,MAAM,IAAI,CACd,CACJ,CACO,MAAM,UAAuB,QAChC,YAAY,CAAO,CAAC,CAGhB,KAAK,GACL,IAAI,CAAC,OAAO,CAAG,IAAI,MAAM,EAAS,CAC9B,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EAIvB,GAAoB,UAAhB,AAA0B,OAAnB,EACP,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,GAE5C,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,GAEpE,GAAI,KAAoB,IAAb,EAEX,OAFqC,AAE9B,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAU,EAChD,EACA,IAAK,CAAM,CAAE,CAAI,CAAE,CAAK,CAAE,CAAQ,EAC9B,GAAoB,UAAhB,AAA0B,OAAnB,EACP,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAAO,GAEnD,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,GAEpE,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,GAAY,EAAM,EAAO,EAC/D,EACA,IAAK,CAAM,CAAE,CAAI,EACb,GAAoB,UAAhB,OAAO,EAAmB,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,GAChE,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,UAEpE,IAAI,CAAoB,IAAb,GAEJ,EAAA,IAF8B,OAAO,GAEvB,CAAC,GAAG,CAAC,EAAQ,EACtC,EACA,eAAgB,CAAM,CAAE,CAAI,EACxB,GAAoB,UAAhB,OAAO,EAAmB,OAAO,EAAA,cAAc,CAAC,cAAc,CAAC,EAAQ,GAC3E,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,UAEpE,IAAI,CAAoB,IAAb,GAEJ,EAAA,IAF8B,OAAO,GAEvB,CAAC,cAAc,CAAC,EAAQ,EACjD,CACJ,EACJ,CAIE,OAAO,KAAK,CAAO,CAAE,CACnB,OAAO,IAAI,MAAM,EAAS,CACtB,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EACvB,OAAO,GACH,IAAK,SACL,IAAK,SACL,IAAK,MACD,OAAO,EAAqB,QAAQ,AACxC,SACI,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAChD,CACJ,CACJ,EACJ,CAOE,MAAM,CAAK,CAAE,QACX,AAAI,MAAM,OAAO,CAAC,GAAe,EAAM,GAAb,CAAiB,CAAC,MACrC,CACX,CAME,OAAO,KAAK,CAAO,CAAE,QACnB,AAAI,aAAmB,QAAgB,CAAP,CACzB,IAAI,EAAe,EAC9B,CACA,OAAO,CAAI,CAAE,CAAK,CAAE,CAChB,IAAM,EAAW,IAAI,CAAC,OAAO,CAAC,EAAK,CACX,UAAU,AAA9B,OAAO,EACP,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CACjB,EACA,EACH,CACM,MAAM,OAAO,CAAC,GACrB,EAAS,IAAI,CAAC,CADkB,EAGhC,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CAE7B,CACA,OAAO,CAAI,CAAE,CACT,OAAO,IAAI,CAAC,OAAO,CAAC,EAAK,AAC7B,CACA,IAAI,CAAI,CAAE,CACN,IAAM,EAAQ,IAAI,CAAC,OAAO,CAAC,EAAK,QAChC,AAAI,KAAiB,IAAV,EAA8B,IAAI,CAAC,EAAZ,GAAiB,CAAC,GAC7C,IACX,CACA,IAAI,CAAI,CAAE,CACN,OAAO,KAA8B,IAAvB,IAAI,CAAC,OAAO,CAAC,EAAK,AACpC,CACA,IAAI,CAAI,CAAE,CAAK,CAAE,CACb,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CACzB,CACA,QAAQ,CAAU,CAAE,CAAO,CAAE,CACzB,IAAK,GAAM,CAAC,EAAM,EAAM,GAAI,IAAI,CAAC,OAAO,GAAG,AACvC,EAAW,IAAI,CAAC,EAAS,EAAO,EAAM,IAAI,CAElD,CACA,CAAC,SAAU,CACP,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CACxC,IAAM,EAAO,EAAI,WAAW,GAGtB,EAAQ,IAAI,CAAC,GAAG,CAAC,EACvB,MAAM,CACF,EACA,EACH,AACL,CACJ,CACA,CAAC,MAAO,CACJ,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CACxC,IAAM,EAAO,EAAI,WAAW,EAC5B,OAAM,CACV,CACJ,CACA,CAAC,QAAS,CACN,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAGxC,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,EACvB,OAAM,CACV,CACJ,CACA,CAAC,OAAO,QAAQ,CAAC,EAAG,CAChB,OAAO,IAAI,CAAC,OAAO,EACvB,CACJ,CExKA,CF0KA,CE1KA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAuDmC,GFiHA,IEjHO,AAFA,CAAC,mBAAmB,CAAC,EAGxD,IAAM,EAAyB,OAAO,AAJD,CAAC,kBAAkB,CAAC,ECtDhE,IAAA,EAAA,EAAA,CAAA,CAAA,OFDA,EAAA,EAAA,CAAA,CAAA,MAEO,OAAM,EACT,YAAY,CAAM,CAAE,CAAG,CAAE,CAAI,CAAC,CAC1B,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,GAAG,CAAG,EACX,IAAI,CAAC,IAAI,CAAG,CAChB,CAEA,IAAI,SAAU,cACV,AAAI,IAAI,CAAC,QAAQ,CAAS,CAAP,GAAW,CAAC,QAAQ,CAChC,IAAI,CAAC,QAAQ,CAAG,CGRK,EHQW,IAAI,CGRR,AHQS,OAAO,CGPhD,SAAS,EACZ,GAAM,QAAE,CAAM,CAAE,CAAG,EACnB,GAAI,CAAC,EACD,MADS,AACF,CAAC,EAEZ,GAAM,CAAE,MAAO,CAAa,CAAE,CAAA,EAAA,CAAA,CAAA,OAC9B,OAAO,EAAc,MAAM,OAAO,CAAC,GAAU,EAAO,IAAI,CAAC,MAAQ,EACrE,IHCA,CACJ,CACO,MAAM,EACT,YAAY,CAAW,CAAC,CACpB,IAAI,CAAC,WAAW,CAAG,CACvB,CAEA,SAAS,CAAW,CAAE,CAAU,CAAE,CAQ9B,OAPA,IAAI,CAAC,SAAS,CAAC,WAAY,GAC3B,IAAI,CAAC,UAAU,CAAG,EAGd,IAAe,EAAA,kBAAkB,CAAC,iBAAiB,EAAE,AACrD,IAAI,CAAC,SAAS,CAAC,UAAW,CAAC,MAAM,EAAE,EAAA,CAAa,EAE7C,IAAI,AACf,CACJ,CEzBO,CF2BP,KE3Ba,UAAwB,EACjC,QAAO,CAAE,AAAF,CAAY,EAAP,AAA4B,EAAA,EF0BX,eE1B4B,AAAC,AAC1D,aAAY,CAAI,CAAC,CACb,IAAI,EACJ,KAAK,CAAC,EAAK,MAAM,CAAC,WAAW,GAAI,EAAK,GAAG,CAAE,GAAO,IAAI,CAAC,IAAI,CAAG,EAAM,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,YAAY,CAA+B,AAA5B,OAAC,EAAa,IAAI,CAAC,IAAI,AAAJ,EAAgB,KAAK,EAAI,EAAW,YAAY,CAAE,IAAI,CAAC,EAAmB,CAAG,IAAI,CAAC,IAAI,CAAC,EAAA,iBAAiB,CAAC,EAAI,CAAC,EAAG,IAAI,CAAC,SAAS,EAAG,CACnR,CACA,IAAI,iBAAkB,CAMlB,OAHA,IAAI,CAAC,IAAI,CAAC,EAAA,iBAAiB,CAAC,CAAG,IAAI,CAAC,EAAA,iBAAiB,CAAC,CACtD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,IAAI,CAAC,GAAG,CACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CACzB,IAAI,CAAC,IAAI,AACpB,CACA,IAAI,gBAAgB,CAAK,CAAE,CACvB,IAAI,CAAC,IAAI,CAAG,CAChB,CAOE,QAAS,CACP,GAAI,IAAI,CAAC,SAAS,CACd,CADgB,KACV,OAAO,cAAc,CAAC,AAAI,MAAM,+DAAgE,oBAAqB,CACvH,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAGJ,OADA,IAAI,CAAC,SAAS,EAAG,EACV,IAAI,eAAe,CACtB,MAAO,AAAC,IACJ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAQ,AAAC,IAClB,EAAW,OAAO,CAAC,IAAI,WAAW,GACtC,GACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAO,KAChB,EAAW,KAAK,EACpB,GACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAS,AAAC,IACnB,EAAW,KAAK,CAAC,EACrB,EACJ,CACJ,EACJ,CACJ,CACO,MAAM,UAAyB,EAClC,IAAI,kBAAmB,CAInB,OAHI,KAA0B,IAAI,EAAE,CAChC,IAAI,CAAC,IAAI,CAAC,EAAuB,CAAG,IAAI,CAAC,EAAA,AAAuB,EAE7D,IAAI,CAAC,IAAI,AACpB,CACA,YAAY,CAAI,CAAC,CACb,KAAK,CAAC,GAAO,IAAI,CAAC,IAAI,CAAG,EAAM,IAAI,CAAC,QAAQ,MAAG,CACnD,CACA,IAAI,MAAO,CACP,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,IAAI,CAAC,WAAW,AACtD,CACA,IAAI,YAAa,CACb,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,AAC/B,CACA,IAAI,WAAW,CAAK,CAAE,CAClB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAG,CAC3B,CACA,IAAI,eAAgB,CAChB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,AAClC,CACA,IAAI,cAAc,CAAK,CAAE,CACrB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAG,CAC9B,CACA,UAAU,CAAI,CAAE,CAAK,CAAE,CAEnB,OADA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAM,GACnB,IACX,AADe,CAEf,aAAa,CAAI,CAAE,CAEf,OADA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAChB,IAAI,AACf,CACA,gBAAgB,CAAI,CAAE,CAClB,IAAM,EAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GACnC,GAAI,KAAW,MACf,KAD0B,CACnB,CAAC,KADyB,CACnB,OAAO,CAAC,GAAU,EAAS,CACrC,EACH,EAAE,GAAG,CAAC,AAAC,GAAQ,EAAM,QAAQ,GAClC,CACA,UAAU,CAAI,CAAE,CACZ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAC/B,CACA,UAAU,CAAI,CAAE,CACZ,IAAM,EAAS,IAAI,CAAC,eAAe,CAAC,GACpC,OAAO,MAAM,OAAO,CAAC,GAAU,EAAO,IAAI,CAAC,UAAO,CACtD,CACA,YAAa,CACT,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAC/B,CACA,aAAa,CAAI,CAAE,CAAK,CAAE,CACtB,IAAM,EAAgB,IAAI,CAAC,eAAe,CAAC,IAAS,EAAE,CAOtD,OANI,AAAC,EAAc,QAAQ,CAAC,IACxB,IADgC,AAC5B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAM,IACnB,EACH,EACH,EAEE,IACX,AADe,CAEf,KAAK,CAAK,CAAE,CAER,OADA,IAAI,CAAC,QAAQ,CAAG,EACT,IACX,AADe,CAEf,MAAO,CACH,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAC/B,CACA,QAAQ,CAAQ,CAAE,CACd,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,QAAS,EACtC,CACJ,gEExHA,IAAA,EAAA,EAAA,CAAA,CAAA,OAmBW,SAAS,EAAiB,CAAK,QACtC,MCjBO,CDiBA,AClB4B,EDkBT,EAAM,AClBO,KDkBF,CAAC,KAAK,MAAM,CAAC,CAAC,EAAU,EAAS,EAAO,IAEzE,AAAI,CAAC,GAID,CAAA,EAAA,EAAA,CAJU,aAIV,AAAc,EAAC,IAIA,KAAK,CAJK,AAIzB,CAAO,CAAC,EAAE,EAIV,CAAa,SAAZ,GAAsB,AAAY,WAAA,CAAO,EAAK,IAAU,EAAS,MAAM,CAAG,EAXpE,CAWuE,CAG3E,EAAW,IAAM,EACzB,KCnCS,UAAU,CAAC,KAAO,EAAO,IAAM,CDoC/C,+DExCA,IAAA,EAA+B,EAAqB,CAA3CE,AAA2C,CAAA,IAAA,GAgB7C,MAhBgB,GAgBPC,EAAsB,GAhBP,SAiB7BC,CAAU,QACVC,CAAM,CACO,EACb,IAAMC,EACkB,UAAtB,OAAOF,QACIH,IAAXI,GACAD,EAAaC,EACT,CAAC,yBAAyB,EAAEA,EAASD,EAAAA,CAAY,CACjD,UAEN,AAAmB,GAAG,CAAlBA,EACK,0DACwB,UAAU,AAAhC,OAAOA,EACT,CAAC,SAAS,EAAEA,EAAAA,EAAaE,EAAAA,CAAW,CAGtC,CAAC,SAAS,EAAEJ,EAAAA,cAAAA,CAAAA,EAAiBI,EAAAA,CAAW,AACjD,oHClCA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,CAAQ,UAAU,iFACjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yCACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4DAAmD,yCAGjE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,kDAAyC,+BAGtD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yDAAgD,6MAI7D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,yCAAgC,wBAGhE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,YAAY,UAAU,2CAAkC,0BAQzE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oGAIvB,CCjCA,IAAM,EAAW,CACf,CACE,MAAO,kBACP,YAAa,+DACb,KAAM,4BACN,KAAM,IACR,EACA,CACE,MAAO,uBACP,YAAa,8CACb,KAAM,iCACN,KAAM,IACR,EACA,CACE,MAAO,mBACP,YAAa,iDACb,KAAM,6BACN,KAAM,KACR,EACA,CACE,MAAO,2BACP,YAAa,gDACb,KAAM,qCACN,KAAM,IACR,EACA,CACE,MAAO,sBACP,YAAa,+DACb,KAAM,gCACN,KAAM,IACR,EACD,CAEc,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,4BACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,gFAAuE,iBAGrF,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mDAA0C,4IAMzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gEACZ,EAAS,GAAG,CAAC,CAAC,EAAS,IACtB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CAEH,KAAM,EAAQ,IAAI,CAClB,UAAU,mGAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAiB,EAAQ,IAAI,GAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uHACX,EAAQ,KAAK,GAEhB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BACV,EAAQ,WAAW,GAEtB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iEAAwD,mBAXlE,UAoBnB,CCxEA,IAAM,EAAW,CACf,CACE,MAAO,yBACP,YAAa,mGACb,KAAM,GACR,EACA,CACE,MAAO,yBACP,YAAa,0FACb,KAAM,IACR,EACA,CACE,MAAO,mBACP,YAAa,iFACb,KAAM,KACR,EACA,CACE,MAAO,sBACP,YAAa,wFACb,KAAM,IACR,EACD,CAEc,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,0BACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,gFAAuE,uCAGrF,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mDAA0C,4IAMzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gEACZ,EAAS,GAAG,CAAC,CAAC,EAAS,IACtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAgB,UAAU,wBACzB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAiB,EAAQ,IAAI,GAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uEACX,EAAQ,KAAK,GAEhB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBACV,EAAQ,WAAW,KANd,UActB,CClDA,IAAM,EAAe,CACnB,CACE,GAAI,EACJ,MAAO,4BACP,SAAU,UACV,MAAO,2BACP,IAAK,uCACP,EACA,CACE,GAAI,EACJ,MAAO,iCACP,SAAU,WACV,MAAO,4BACP,IAAK,iDACP,EACA,CACE,GAAI,EACJ,MAAO,kBACP,SAAU,OACV,MAAO,wBACP,IAAK,6CACP,EACA,CACE,GAAI,EACJ,MAAO,2BACP,SAAU,aACV,MAAO,8BACP,IAAK,uCACP,EACA,CACE,GAAI,EACJ,MAAO,2BACP,SAAU,UACV,MAAO,2BACP,IAAK,kCACP,EACA,CACE,GAAI,EACJ,MAAO,+BACP,SAAU,UACV,MAAO,4BACP,IAAK,uCACP,EACD,CAEc,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,4BACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,gFAAuE,oBAGrF,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mDAA0C,uIAMzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qEACZ,EAAa,GAAG,CAAC,AAAC,GACjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAkB,UAAU,gCAC3B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yGAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,OAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+BAAuB,EAAK,KAAK,GAChD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mBAAW,EAAK,QAAQ,QAG3C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+FACf,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kHACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAiB,EAAK,KAAK,GACzC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mBAAW,EAAK,QAAQ,UAbjC,EAAK,EAAE,KAoBrB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,YAAY,UAAU,uBAAc,4BAOzD,CC5Fe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,8CACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CAAkC,MACjD,CAAA,EAAA,EAAA,GAAA,EAAC,aAAA,CAAW,UAAU,sDAA6C,+OAKnE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,oBAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,yCAInC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0BACZ,oCAAa,CAAC,GAAG,CAAC,CAAC,EAAG,IACrB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAa,UAAU,sCAA6B,KAA1C,QAKjB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,yGAIlC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,gBAAgB,UAAU,uBAAc,sBAGhD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,WAAW,UAAU,uGAA8F,iCAOrI,CCtCe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,4CACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4DAAmD,kCAGjE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mCAA0B,8IAKvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gEACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,kIAAyH,mBAGzJ,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,kBAAkB,UAAU,iKAAwJ,6BAK9L,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+BACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,yDACH,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,8CAKb,CCvBe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,KAGP", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}