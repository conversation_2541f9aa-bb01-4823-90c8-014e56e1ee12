export default function TestimonialSnippet() {
  return (
    <section className="py-16 bg-kozak-charcoal text-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="mb-8">
          <div className="text-kozak-orange text-6xl mb-4">&ldquo;</div>
          <blockquote className="text-xl md:text-2xl font-light italic mb-6">
            <PERSON><PERSON> transformed our outdated kitchen into a stunning modern space. His attention to detail
            and quality of work exceeded our expectations. The project was completed on time and within budget.
            We couldn&apos;t be happier with the results!
          </blockquote>
          <div className="text-lg">
            <div className="font-semibold"><PERSON> & <PERSON>.</div>
            <div className="text-gray-300">Kitchen Renovation, Nottingham</div>
          </div>
        </div>
        
        <div className="flex justify-center mb-6">
          <div className="flex space-x-1">
            {[...Array(5)].map((_, i) => (
              <span key={i} className="text-kozak-orange text-2xl">★</span>
            ))}
          </div>
        </div>

        <p className="text-gray-300 mb-8">
          Join our growing list of satisfied customers across Nottinghamshire, Derbyshire, and Leicestershire.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/testimonials" className="btn-primary">
            Read More Reviews
          </a>
          <a href="/contact" className="btn-secondary bg-transparent border-2 border-white hover:bg-white hover:text-kozak-charcoal">
            Share Your Experience
          </a>
        </div>
      </div>
    </section>
  )
}
